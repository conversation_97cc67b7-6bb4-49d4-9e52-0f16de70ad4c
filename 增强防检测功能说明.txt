# 增强防检测功能说明文档

## 防检测增强功能概述
这些新增的防检测功能可以有效避免您的程序被其他监控软件检测到，特别是提升了图像识别过程的安全性，使程序更难被检测。

## 新增防检测功能列表

### 1. 图像识别保护
- **三种识别模式**：
  - **普通模式**：标准的OpenCV图像识别，速度快，但特征明显
  - **安全模式**：分块识别并随机延迟，降低监控风险，平衡性能和安全性
  - **隐身模式**：添加随机噪声并使用多策略匹配，最高级别的保护，但可能略降低性能

### 2. 屏幕截图保护
- **随机截图方法**：程序会随机在3种不同的截图API中切换，避免形成固定的API调用特征
- **安全截图延迟**：在每次截图操作中添加微小的随机延迟，模拟人类操作
- **低级别截图API**：使用Windows原生API进行截图，更难被检测

### 3. 内存保护
- **防止内存扫描**：通过多种技术阻止其他程序读取本程序的内存内容
- **内存特征掩盖**：创建随机大小的内存对象来混淆内存特征，防止特征识别
- **随机内存清理**：在随机时间间隔自动清理内存，降低留下痕迹的风险

### 4. 进程伪装
- **随机系统进程伪装**：程序可以随机伪装成多种常见系统进程
- **进程名称保护**：定期更改进程显示名称，增加追踪难度
- **时间特征随机化**：操作时间、延迟、识别间隔全部随机化，避免被通过固定时间模式识别

## 使用方法
1. 在设置选项卡中找到"防检测设置"部分
2. 开启"启用防检测功能"选项
3. 在高级防检测选项中，选择适合您需要的保护级别：
   - 对于一般使用，推荐使用"安全模式"进行图像识别
   - 如果担心被检测，可以启用"隐身模式"获得最高级别保护
4. 建议同时开启"随机化截图方法"和"防止内存扫描"功能
5. 如需最高级别保护，请开启所有高级选项

## 注意事项
1. 更高级别的防检测可能会略微降低程序性能
2. "隐身模式"下识别精度可能略有降低，如遇问题可切换到"安全模式"
3. 如果您的电脑配置较低，建议不要同时开启所有防检测功能

## 技术原理
这些防检测功能使用了多种先进技术，包括：
- 图像分块识别和处理
- 内存特征混淆
- API调用随机化
- 低级系统API调用
- 进程特征伪装
- 时间模式随机化

这些技术组合使用，可以有效避免大多数检测软件的监控。 