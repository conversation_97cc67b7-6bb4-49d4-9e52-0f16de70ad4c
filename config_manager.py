import json
import os
import time
import sys

# 配置文件路径
# 判断是否是打包后的环境
def get_application_path():
    if getattr(sys, 'frozen', False):
        # 如果是打包后的环境，使用可执行文件所在目录
        return os.path.dirname(sys.executable)
    else:
        # 如果是开发环境，使用脚本所在目录
        return os.path.dirname(os.path.abspath(__file__))

CONFIG_FILE = os.path.join(get_application_path(), 'config.json')

# 默认配置
DEFAULT_CONFIG = {
    'recognition_threshold': 0.7,  # 图像识别阈值
    'cooldown_time': 10,  # 冷却时间（秒）
    'cooldown_random_range': 0,  # 冷却时间随机范围（秒）
    'sub_threshold_min': 0.61,  # 子框识别最小阈值
    'sub_threshold_max': 0.81,  # 子框识别最大阈值
    'threshold_mode': 'range',  # 阈值模式
    'rect_pid_mapping': {  # 区域进程ID映射
        '0': None,
        '1': None,
        '2': None,
        '3': None
    },
    'sub_rect_coords': {  # 子框坐标（相对于父框的百分比）
        '0': [0.1, 0.1, 0.8, 0.8],
        '1': [0.1, 0.1, 0.8, 0.8],
        '2': [0.1, 0.1, 0.8, 0.8],
        '3': [0.1, 0.1, 0.8, 0.8]
    },
    'new_sub_rect_coords': {  # 新子框坐标（相对于父框的百分比）
        '0': [0.6, 0.6, 0.3, 0.3],
        '1': [0.6, 0.6, 0.3, 0.3],
        '2': [0.6, 0.6, 0.3, 0.3],
        '3': [0.6, 0.6, 0.3, 0.3]
    },
    'new_sub_rect_thresholds': {  # 新子框的识别阈值
        '0': 0.7,
        '1': 0.7,
        '2': 0.7,
        '3': 0.7
    },
    'new_sub_rect_image_folders': {  # 新子框的图像文件夹路径
        '0': None,
        '1': None,
        '2': None,
        '3': None
    },
    'new_sub_rect_cooldown_time': {  # 新子框的冷却时间（秒）
        '0': 3,
        '1': 3,
        '2': 3,
        '3': 3
    },
    'new_sub_cooldown_random_range': 0,  # 新子框冷却时间随机范围（秒）
    'image_folder': None,  # 图像文件夹路径
    'last_saved': None  # 上次保存时间
}


def load_config():
    """
    加载配置文件，如果文件不存在则创建默认配置
    """
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 确保所有必要的配置项都存在
                for key, value in DEFAULT_CONFIG.items():
                    if key not in config:
                        config[key] = value
                return config
        else:
            # 如果配置文件不存在，创建默认配置
            save_config(DEFAULT_CONFIG)
            return DEFAULT_CONFIG.copy()
    except Exception as e:
        print(f"加载配置文件时出错: {e}")
        return DEFAULT_CONFIG.copy()


def save_config(config):
    """
    保存配置到文件
    """
    try:
        # 更新保存时间
        config['last_saved'] = time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 确保目录存在
        os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
        
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"保存配置文件时出错: {e}")
        return False


def update_config(key, value):
    """
    更新单个配置项并保存
    """
    config = load_config()
    config[key] = value
    return save_config(config)


def update_sub_rect_coord(index, coords):
    """
    更新子框坐标配置
    """
    config = load_config()
    config['sub_rect_coords'][str(index)] = coords
    return save_config(config)


def update_new_sub_rect_coord(index, coords):
    """
    更新新子框坐标配置
    """
    config = load_config()
    config['new_sub_rect_coords'][str(index)] = coords
    return save_config(config)


def update_new_sub_rect_threshold(index, threshold):
    """
    更新新子框识别阈值配置
    """
    config = load_config()
    config['new_sub_rect_thresholds'][str(index)] = threshold
    return save_config(config)


def update_new_sub_rect_image_folder(index, folder):
    """
    更新新子框图像文件夹路径配置
    """
    config = load_config()
    config['new_sub_rect_image_folders'][str(index)] = folder
    return save_config(config)


def update_new_sub_rect_cooldown_time(index, cooldown_time):
    """
    更新新子框冷却时间配置
    """
    config = load_config()
    config['new_sub_rect_cooldown_time'][str(index)] = cooldown_time
    return save_config(config)


def update_rect_pid_mapping(rect_pid_mapping):
    """
    更新区域进程ID映射配置
    """
    config = load_config()
    config['rect_pid_mapping'] = {str(i): rect_pid_mapping[i] for i in range(4)}
    return save_config(config)


def update_threshold_settings(sub_threshold_min, sub_threshold_max, threshold_mode):
    """
    更新阈值设置配置
    """
    config = load_config()
    config['sub_threshold_min'] = sub_threshold_min
    config['sub_threshold_max'] = sub_threshold_max
    config['threshold_mode'] = threshold_mode
    return save_config(config)


def update_recognition_threshold(threshold):
    """
    更新识别阈值配置
    """
    config = load_config()
    config['recognition_threshold'] = threshold
    return save_config(config)
