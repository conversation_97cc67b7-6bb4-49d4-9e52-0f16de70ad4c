import tkinter as tk
import win32gui
import win32con
import win32api
import os
import psutil
import cv2
import numpy as np
import pyautogui
import threading
import time
import random
import sys
import ctypes
import subprocess
from tkinter import filedialog, ttk, messagebox, colorchooser
from PIL import Image, ImageTk
import win32process
import win32security
import win32service
import win32ts
import win32event
import win32job
import pywintypes
import winreg
# 导入配置管理模块
import config_manager

# 全局变量
selected_rect_index = None
selected_new_subrect_index = None  # 添加缺失的全局变量
image_folder = None
template_images = []
monitoring_active = False
monitoring_thread = None
log_messages = []  # 全局定义日志信息列表

# UI组件全局引用
templates_listbox = None
folder_var = None
rect_var = None
status_var = None
monitor_button = None
new_sub_folder_vars = {}
new_sub_templates_listboxes = {}

# 存储每个区域计算出的实际随机冷却时间
actual_cooldown_time = {0: 0, 1: 0, 2: 0, 3: 0}
new_sub_actual_cooldown_time = {0: 0, 1: 0, 2: 0, 3: 0}

# 加载配置
config = config_manager.load_config()

# 窗口位置和大小设置
window_position_x = config.get('window_position_x', 100)
window_position_y = config.get('window_position_y', 100)
window_width = config.get('window_width', 2500)  # 增加宽度以适应横向排列的4个区域
window_height = config.get('window_height', 900)  # 减小高度，因为只有一行区域

# 区域大小设置
rect_width = config.get('rect_width', 400)  # 减小单个区域宽度，以便适应屏幕
rect_height = config.get('rect_height', 600)  # 调整高度

# 防检测设置
anti_detection_enabled = config.get('anti_detection_enabled', False)  # 默认关闭
process_name_disguise = config.get('process_name_disguise', "lghub.exe")  # 默认伪装为Logitech Gaming Hub
hide_window_from_taskbar = config.get('hide_window_from_taskbar', False)
hide_window_title = config.get('hide_window_title', False)  # 隐藏窗口标题
hide_process_enabled = config.get('hide_process_enabled', False)  # 隐藏进程
randomize_timing = config.get('randomize_timing', True)
memory_cleaning_interval = config.get('memory_cleaning_interval', 300)  # 秒
memory_cleaning_random = config.get('memory_cleaning_random', True)  # 内存清理随机化
last_memory_cleaning_time = 0

# 增强防检测设置
img_recognize_method = config.get('img_recognize_method', 'normal')  # 图像识别方法: normal, safe, stealth
anti_memory_scan = config.get('anti_memory_scan', True)  # 防止内存扫描
randomize_screenshot = config.get('randomize_screenshot', True)  # 随机化截图方法
secure_process_name = config.get('secure_process_name', True)  # 增强进程名保护
process_disguise_list = config.get('process_disguise_list', [  # 进程伪装列表(随机选择)
    "lghub.exe", "Taskmgr.exe", "explorer.exe", "svchost.exe", "RuntimeBroker.exe", 
    "dwm.exe", "chrome.exe", "msedge.exe", "firefox.exe", "OneDrive.exe"
])
mask_memory_signature = config.get('mask_memory_signature', True)  # 掩盖内存特征

# 图像识别阈值，从配置加载，默认值为0.7
recognition_threshold = config['recognition_threshold']

# 子框识别阈值范围
sub_threshold_min = config.get('sub_threshold_min', 0.61)
sub_threshold_max = config.get('sub_threshold_max', 0.81)

# 阈值模式选择：'fixed' 为固定阈值模式，'range' 为范围阈值模式
threshold_mode = config.get('threshold_mode', 'range')

# 存储每个矩形区域对应的进程ID - 现在保存到配置文件
rect_pid_mapping = {}
for i in range(4):
    rect_pid_mapping[i] = config.get('rect_pid_mapping', {}).get(str(i), None)

# 存储显示每个区域PID的变量
rect_pid_vars = None

# 每个区域的冷却时间（秒），从配置加载
cooldown_time = config['cooldown_time']

# 随机冷却时间范围（秒），从配置加载，默认值为0（不随机）
cooldown_random_range = config.get('cooldown_random_range', 0)

# 存储每个区域的上次触发时间 - 不保存到配置文件
last_trigger_time = {0: 0, 1: 0, 2: 0, 3: 0}

# 存储每个区域的冷却状态 - 不保存到配置文件
area_in_cooldown = {0: False, 1: False, 2: False, 3: False}

# 存储子框的暂停状态 - 当新子框识别到图像时暂停子框识别
sub_rect_paused = {0: False, 1: False, 2: False, 3: False}

# 存储子框的坐标信息，格式为：左上角x, 左上角y, 宽度, 高度（相对于父框的百分比）
# 从配置加载子框坐标
sub_rect_coords = {}
for i in range(4):
    sub_rect_coords[i] = config['sub_rect_coords'][str(i)]

# 存储子框的引用
sub_rectangles = [None, None, None, None]

# 存储子框的标签引用
sub_text_labels = [None, None, None, None]

# 存储新子框的坐标信息，格式为：左上角x, 左上角y, 宽度, 高度（相对于父框的百分比）
# 从配置加载新子框坐标
new_sub_rect_coords = {}
for i in range(4):
    new_sub_rect_coords[i] = config['new_sub_rect_coords'][str(i)]

# 存储新子框的引用
new_sub_rectangles = [None, None, None, None]

# 存储新子框的标签引用
new_sub_text_labels = [None, None, None, None]

# 存储新子框的图像文件夹路径
new_sub_image_folders = {}
for i in range(4):
    new_sub_image_folders[i] = config['new_sub_rect_image_folders'][str(i)]

# 存储新子框的模板图像
new_sub_template_images = {0: [], 1: [], 2: [], 3: []}

# 存储新子框的识别阈值
new_sub_recognition_thresholds = {}
for i in range(4):
    new_sub_recognition_thresholds[i] = config['new_sub_rect_thresholds'][str(i)]

# 存储新子框的冷却时间
new_sub_cooldown_times = {}
for i in range(4):
    new_sub_cooldown_times[i] = config['new_sub_rect_cooldown_time'][str(i)]

# 新子框的随机冷却时间范围（秒），从配置加载，默认值为0（不随机）
new_sub_cooldown_random_range = config.get('new_sub_cooldown_random_range', 0)

# 存储新子框的上次触发时间 - 不保存到配置文件
new_sub_last_trigger_time = {0: 0, 1: 0, 2: 0, 3: 0}

# 存储新子框的冷却状态 - 不保存到配置文件
new_sub_area_in_cooldown = {0: False, 1: False, 2: False, 3: False}

# 存储框框的自定义名称
rect_custom_names = {0: "", 1: "", 2: "", 3: ""}
sub_rect_custom_names = {0: "", 1: "", 2: "", 3: ""}
new_sub_rect_custom_names = {0: "", 1: "", 2: "", 3: ""}

# 从配置中加载自定义名称（如果存在）
if 'rect_custom_names' in config:
    for i in range(4):
        if str(i) in config['rect_custom_names']:
            rect_custom_names[i] = config['rect_custom_names'][str(i)]

if 'sub_rect_custom_names' in config:
    for i in range(4):
        if str(i) in config['sub_rect_custom_names']:
            sub_rect_custom_names[i] = config['sub_rect_custom_names'][str(i)]

if 'new_sub_rect_custom_names' in config:
    for i in range(4):
        if str(i) in config['new_sub_rect_custom_names']:
            new_sub_rect_custom_names[i] = config['new_sub_rect_custom_names'][str(i)]

# 存储框框的自定义颜色
rect_custom_colors = {0: "#5B9BD5", 1: "#5B9BD5", 2: "#5B9BD5", 3: "#5B9BD5"}
sub_rect_custom_colors = {0: "#16A085", 1: "#16A085", 2: "#16A085", 3: "#16A085"}
new_sub_rect_custom_colors = {0: "#ff00ff", 1: "#ff00ff", 2: "#ff00ff", 3: "#ff00ff"}

# 从配置中加载自定义颜色（如果存在）
if 'rect_custom_colors' in config:
    for i in range(4):
        if str(i) in config['rect_custom_colors']:
            rect_custom_colors[i] = config['rect_custom_colors'][str(i)]

if 'sub_rect_custom_colors' in config:
    for i in range(4):
        if str(i) in config['sub_rect_custom_colors']:
            sub_rect_custom_colors[i] = config['sub_rect_custom_colors'][str(i)]

if 'new_sub_rect_custom_colors' in config:
    for i in range(4):
        if str(i) in config['new_sub_rect_custom_colors']:
            new_sub_rect_custom_colors[i] = config['new_sub_rect_custom_colors'][str(i)]

# 存储新子框的禁止操作时间（秒）
new_sub_block_times = {}
for i in range(4):
    # 确保使用与冷却时间相同的值，保持一致性
    new_sub_block_times[i] = new_sub_cooldown_times[i]

# 初始定义update_log_display函数，防止引用错误
def update_log_display():
    """
    更新日志显示的初始函数，防止引用错误
    这个函数将在UI创建后被重新赋值
    """
    global log_messages
    # 在UI创建前，只将日志输出到控制台
    if log_messages:
        print(log_messages[-1])

# 防检测功能
def enable_anti_detection():
    global anti_detection_enabled, log_messages
    anti_detection_enabled = True
    config_manager.update_config('anti_detection_enabled', True)
    
    # 隐藏窗口标题栏
    if hide_window_title:
        try:
            hwnd = win32gui.GetForegroundWindow()
            # 将窗口标题修改为空字符串
            win32gui.SetWindowText(hwnd, "")
            # 添加日志记录
            log_message = f"[{time.strftime('%H:%M:%S')}] 窗口标题已隐藏"
            log_messages.append(log_message)
            try:
                update_log_display()
            except:
                print(log_message)
        except Exception as e:
            print(f"隐藏窗口标题失败: {e}")
    
    # 隐藏窗口从任务栏
    if hide_window_from_taskbar:
        try:
            hwnd = win32gui.GetForegroundWindow()
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, 
                                win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE) | win32con.WS_EX_TOOLWINDOW)
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
        except Exception as e:
            print(f"隐藏窗口失败: {e}")
    
    # 隐藏进程功能
    if hide_process_enabled:
        try:
            # 获取当前进程ID和句柄
            current_pid = os.getpid()
            
            # 尝试修改进程名称
            try:
                # 创建一个作业对象，将当前进程添加到该作业中
                job_handle = win32job.CreateJobObject(None, f"HiddenJob_{current_pid}")
                
                # 注意：Windows 11或新版Python中，以下代码可能出错
                try:
                    # 创建作业信息结构体
                    limit_info = win32job.JOBOBJECT_BASIC_LIMIT_INFORMATION()
                    limit_info.LimitFlags = win32job.JOB_OBJECT_LIMIT_SILENT_BREAKAWAY_OK | win32job.JOB_OBJECT_LIMIT_PRIORITY_CLASS
                    limit_info.PriorityClass = win32process.IDLE_PRIORITY_CLASS
                    
                    # 创建扩展限制信息结构体
                    io_info = win32job.JOBOBJECT_EXTENDED_LIMIT_INFORMATION()
                    io_info.BasicLimitInformation = limit_info
                    
                    # 保存作业设置
                    try:
                        win32job.SetInformationJobObject(job_handle, win32job.JobObjectExtendedLimitInformation, io_info)
                        
                        # 将当前进程添加到作业
                        process_handle = win32api.OpenProcess(win32con.PROCESS_ALL_ACCESS, False, current_pid)
                        win32job.AssignProcessToJobObject(job_handle, process_handle)
                    except Exception as e:
                        print(f"设置作业信息失败 (不影响主要功能): {e}")
                    
                except Exception as e:
                    print(f"创建作业信息失败 (不影响主要功能): {e}")
                
                # 添加日志记录
                log_message = f"[{time.strftime('%H:%M:%S')}] 进程隐藏功能已启用"
                log_messages.append(log_message)
                try:
                    update_log_display()
                except:
                    print(log_message)
            except Exception as e:
                print(f"隐藏进程失败: {e}")
                
            # 修改进程优先级为低，使其不易被检测
            process = psutil.Process(current_pid)
            process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            
            # 随机设置CPU亲和性，避免使用所有核心
            cpu_count = psutil.cpu_count()
            if cpu_count > 1:
                # 随机选择一半或更少的CPU核心
                core_count = random.randint(1, max(1, cpu_count // 2))
                cores = random.sample(range(cpu_count), core_count)
                mask = sum(1 << i for i in cores)
                process.cpu_affinity([i for i in cores])
        except Exception as e:
            print(f"修改进程属性失败: {e}")
    
    # 添加日志记录
    log_message = f"[{time.strftime('%H:%M:%S')}] 防检测功能已启用"
    log_messages.append(log_message)
    try:
        update_log_display()
    except:
        print(log_message)

def disable_anti_detection():
    global anti_detection_enabled, log_messages
    anti_detection_enabled = False
    config_manager.update_config('anti_detection_enabled', False)
    
    # 恢复窗口标题栏，使用原始标题
    if hide_window_title:
        try:
            hwnd = win32gui.GetForegroundWindow()
            win32gui.SetWindowText(hwnd, "屏幕区域监控工具")
        except Exception as e:
            print(f"恢复窗口标题失败: {e}")
    
    # 恢复窗口标题栏
    if hide_window_from_taskbar:
        try:
            hwnd = win32gui.GetForegroundWindow()
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, 
                                win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE) & ~win32con.WS_EX_TOOLWINDOW)
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
        except Exception as e:
            print(f"恢复窗口失败: {e}")
    
    # 添加日志记录
    log_message = f"[{time.strftime('%H:%M:%S')}] 防检测功能已禁用"
    log_messages.append(log_message)
    try:
        update_log_display()
    except:
        print(log_message)

def clean_memory():
    """清理内存，减少内存占用痕迹"""
    global last_memory_cleaning_time, log_messages
    
    current_time = time.time()
    
    # 如果启用了随机化内存清理，则计算随机清理间隔
    if memory_cleaning_random:
        # 在设定间隔的基础上增加随机偏差，±15%
        random_interval = memory_cleaning_interval * (1 + random.uniform(-0.15, 0.15))
        required_interval = random_interval
    else:
        required_interval = memory_cleaning_interval
    
    # 检查是否到达清理时间
    if current_time - last_memory_cleaning_time < required_interval:
        return
    
    last_memory_cleaning_time = current_time
    
    try:
        # 强制垃圾回收
        import gc
        gc.collect()
        
        # 在Windows上，尝试使用系统API减少工作集
        if sys.platform == 'win32':
            # 获取当前进程句柄
            current_process = ctypes.windll.kernel32.GetCurrentProcess()
            # 减少工作集
            ctypes.windll.psapi.EmptyWorkingSet(current_process)
        
        # 添加日志记录
        clean_time = "随机" if memory_cleaning_random else "固定"
        log_message = f"[{time.strftime('%H:%M:%S')}] 内存清理完成 ({clean_time}间隔)"
        log_messages.append(log_message)
        try:
            update_log_display()
        except:
            print(log_message)
    except Exception as e:
        print(f"内存清理失败: {e}")

def randomize_execution_timing():
    """随机化执行时间，避免被检测到规律性操作"""
    if not randomize_timing:
        return 0
    
    # 生成一个微小的随机延迟 (0-50ms)
    delay = random.uniform(0, 0.05)
    time.sleep(delay)
    return delay

def create_window():
    # 创建控制面板窗口，使用更现代的设计
    control_panel = tk.Tk()
    
    # 设置窗口标题，如果启用了隐藏标题，则设为空
    if hide_window_title and anti_detection_enabled:
        control_panel.title("")
    else:
        control_panel.title("屏幕区域监控工具")
    
    control_panel.geometry(f"{window_width}x{window_height}+{window_position_x}+{window_position_y}")  # 使用保存的窗口大小和位置
    control_panel.resizable(True, True)
    control_panel.attributes("-topmost", True)
    control_panel.configure(bg="#F5F9FF")  # 使用更明亮的淡蓝色背景
    
    # 初始化rect_pid_vars变量
    global rect_pid_vars
    rect_pid_vars = [tk.StringVar() for _ in range(4)]
    for i in range(4):
        rect_pid_vars[i].set(f"{rect_custom_names[i]} PID: 未选择")
    
    # 设置窗口关闭事件
    def on_closing():
        # 停止监控线程
        global monitoring_active
        monitoring_active = False
        if monitoring_thread and monitoring_thread.is_alive():
            monitoring_thread.join(timeout=1.0)
        
        # 保存窗口位置和大小到配置
        config_manager.update_config('window_position_x', control_panel.winfo_x())
        config_manager.update_config('window_position_y', control_panel.winfo_y())
        config_manager.update_config('window_width', control_panel.winfo_width())
        config_manager.update_config('window_height', control_panel.winfo_height())
        
        # 销毁窗口
        control_panel.destroy()
    
    # 绑定窗口关闭事件
    control_panel.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 设置应用程序图标和样式
    style = ttk.Style()
    style.theme_use('clam')  # 使用更现代的主题
    
    # 定义主题颜色 - 使用更现代、优雅的配色方案
    primary_color = "#4A6FA5"  # 深蓝色主题，更柔和
    secondary_color = "#5B9BD5"  # 亮蓝色，更清新
    accent_color = "#16A085"  # 青绿色强调色，更鲜明
    bg_color = "#F5F9FF"  # 淡蓝色背景，更明亮
    text_color = "#2E4053"  # 深灰蓝色文本，更清晰
    button_hover_color = "#7FB3D5"  # 按钮悬停颜色
    button_pressed_color = "#2874A6"  # 按钮按下颜色
    success_color = "#27AE60"  # 成功状态颜色
    warning_color = "#F39C12"  # 警告状态颜色
    error_color = "#E74C3C"  # 错误状态颜色
    
    # 更新日志显示函数
    def update_log_display():
        # 清空当前日志显示
        log_textbox.config(state=tk.NORMAL)
        log_textbox.delete(1.0, tk.END)
        
        # 显示最新的日志消息（最多显示最近的50条）
        for message in log_messages[-50:]:
            log_textbox.insert(tk.END, message + "\n")
        
        # 滚动到最新消息
        log_textbox.see(tk.END)
        log_textbox.config(state=tk.DISABLED)  # 设置为只读
    
    # 配置样式 - 更现代、精致的按钮样式
    style.configure("TButton", padding=8, relief="flat", background=primary_color, foreground="white", font=("Microsoft YaHei", 10), borderwidth=0)
    style.map("TButton", background=[("active", button_hover_color), ("pressed", button_pressed_color)])
    
    # 强调按钮样式
    style.configure("Accent.TButton", background=accent_color, borderwidth=0)
    style.map("Accent.TButton", background=[("active", "#1ABC9C"), ("pressed", "#0E6655")])
    
    # 成功按钮样式
    style.configure("Success.TButton", background=success_color, foreground="white", borderwidth=0)
    style.map("Success.TButton", background=[("active", "#2ECC71"), ("pressed", "#27AE60")])
    
    # 警告按钮样式
    style.configure("Warning.TButton", background=warning_color, foreground="white", borderwidth=0)
    style.map("Warning.TButton", background=[("active", "#F5B041"), ("pressed", "#E67E22")])
    
    # 框架样式
    style.configure("TFrame", background=bg_color)
    
    # 标签样式
    style.configure("TLabel", background=bg_color, foreground=text_color, font=("Microsoft YaHei", 10))
    style.configure("Header.TLabel", font=("Microsoft YaHei", 12, "bold"), foreground=primary_color)
    style.configure("Status.TLabel", font=("Microsoft YaHei", 9), foreground="#555555")
    
    # 选项卡样式 - 更现代的外观
    style.configure("TNotebook", background=bg_color, tabmargins=[2, 5, 2, 0])
    style.configure("TNotebook.Tab", padding=[12, 6], font=("Microsoft YaHei", 10), background=bg_color, foreground=text_color, borderwidth=0)
    style.map("TNotebook.Tab", background=[("selected", primary_color)], foreground=[("selected", "white")])
    
    # 标签框样式 - 添加圆角效果
    style.configure("TLabelframe", background=bg_color, foreground=primary_color, borderwidth=1, relief="groove")
    style.configure("TLabelframe.Label", font=("Microsoft YaHei", 10, "bold"), background=bg_color, foreground=primary_color)
    
    # 列表框样式 - 更精致的外观
    style.configure("TListbox", background="white", foreground=text_color, borderwidth=1, relief="solid")
    
    # 进度条样式
    style.configure("Horizontal.TProgressbar", background=accent_color, bordercolor=bg_color, lightcolor=secondary_color, darkcolor=primary_color)
    
    # 创建主框架
    main_frame = ttk.Frame(control_panel)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 创建选项卡
    tab_control = ttk.Notebook(main_frame)
    
    # 主控制选项卡
    control_tab = ttk.Frame(tab_control)
    tab_control.add(control_tab, text="主控制")
    
    # 设置选项卡
    settings_tab = ttk.Frame(tab_control)
    tab_control.add(settings_tab, text="设置")
    
    # 帮助选项卡
    help_tab = ttk.Frame(tab_control)
    tab_control.add(help_tab, text="帮助")
    
    tab_control.pack(expand=True, fill=tk.BOTH)
    
    # 获取屏幕尺寸
    screen_width = win32api.GetSystemMetrics(0)
    screen_height = win32api.GetSystemMetrics(1)
    
    # 创建透明全屏窗口用于绘制边框
    overlay = tk.Toplevel()
    overlay.title("")
    overlay.geometry(f"{screen_width}x{screen_height}+0+0")
    overlay.attributes("-alpha", 0.7)  # 设置适当的透明度
    overlay.attributes("-topmost", True)  # 窗口置顶
    overlay.overrideredirect(True)  # 移除窗口边框和标题栏
    overlay.withdraw()  # 初始时隐藏窗口
    
    # 边框颜色和宽度 - 更新为渐变蓝色主题
    border_color = "#5B9BD5"  # 更柔和的蓝色边框
    border_width = 4
    
    # 创建一个Canvas作为全屏画布
    canvas = tk.Canvas(overlay, width=screen_width, height=screen_height, 
                      highlightthickness=0, bg="black", bd=0)
    canvas.pack()
    
    # 设置画布背景为透明
    overlay.wm_attributes("-transparentcolor", "black")
    
    # 计算四个矩形的位置，使其排列为2x2布局（上面两个，下面两个）
    global rect_width, rect_height
    
    # 重新计算间距 - 对于4个水平排列的矩形
    h_spacing = (screen_width - rect_width * 4) / 5
    
    # 垂直居中
    v_offset1 = (screen_height - rect_height) / 2
    
    # 计算每个矩形的水平位置
    h_offset1 = h_spacing
    h_offset2 = h_spacing*2 + rect_width
    h_offset3 = h_spacing*3 + rect_width*2
    h_offset4 = h_spacing*4 + rect_width*3
    
    # 第一个矩形
    rect1 = canvas.create_rectangle(
        h_offset1, v_offset1,
        h_offset1 + rect_width, v_offset1 + rect_height, 
        outline=rect_custom_colors[0], width=border_width, fill='')
    
    # 第二个矩形
    rect2 = canvas.create_rectangle(
        h_offset2, v_offset1,
        h_offset2 + rect_width, v_offset1 + rect_height, 
        outline=rect_custom_colors[1], width=border_width, fill='')
    
    # 第三个矩形
    rect3 = canvas.create_rectangle(
        h_offset3, v_offset1,
        h_offset3 + rect_width, v_offset1 + rect_height, 
        outline=rect_custom_colors[2], width=border_width, fill='')
    
    # 第四个矩形
    rect4 = canvas.create_rectangle(
        h_offset4, v_offset1,
        h_offset4 + rect_width, v_offset1 + rect_height, 
        outline=rect_custom_colors[3], width=border_width, fill='')
    
    # 创建矩形标签
    text_font = ("Arial", 36, "bold")
    sub_text_font = ("Arial", 24, "bold")
    
    # 为每个矩形创建对应的标签
    text1 = canvas.create_text(
        h_offset1 + 30, v_offset1 + 30,
        text=rect_custom_names[0], fill=rect_custom_colors[0], font=text_font)
    
    text2 = canvas.create_text(
        h_offset2 + 30, v_offset1 + 30,
        text=rect_custom_names[1], fill=rect_custom_colors[1], font=text_font)
    
    text3 = canvas.create_text(
        h_offset3 + 30, v_offset1 + 30,
        text=rect_custom_names[2], fill=rect_custom_colors[2], font=text_font)
    
    text4 = canvas.create_text(
        h_offset4 + 30, v_offset1 + 30,
        text=rect_custom_names[3], fill=rect_custom_colors[3], font=text_font)
    
    # 存储所有矩形和文本的引用
    rectangles = [rect1, rect2, rect3, rect4]
    text_labels = [text1, text2, text3, text4]
    
    # 存储矩形的坐标信息，用于图像识别
    rect_coords = [
        (int(h_offset1), int(v_offset1), int(h_offset1 + rect_width), int(v_offset1 + rect_height)),
        (int(h_offset2), int(v_offset1), int(h_offset2 + rect_width), int(v_offset1 + rect_height)),
        (int(h_offset3), int(v_offset1), int(h_offset3 + rect_width), int(v_offset1 + rect_height)),
        (int(h_offset4), int(v_offset1), int(h_offset4 + rect_width), int(v_offset1 + rect_height))
    ]
    
    # 创建更新矩形位置和大小的函数
    def update_rect_dimensions():
        global rect_width, rect_height
        
        # 重新计算间距 - 对于4个水平排列的矩形
        h_spacing = (screen_width - rect_width * 4) / 5
        
        # 垂直居中
        v_offset1 = (screen_height - rect_height) / 2
        
        # 计算每个矩形的水平位置
        h_offset1 = h_spacing
        h_offset2 = h_spacing*2 + rect_width
        h_offset3 = h_spacing*3 + rect_width*2
        h_offset4 = h_spacing*4 + rect_width*3
        
        # 更新四个矩形边框的位置和大小
        # 从左向右依次排列的四个矩形
        canvas.coords(rect1, 
                     h_offset1, v_offset1,
                     h_offset1 + rect_width, v_offset1 + rect_height)
        
        canvas.coords(rect2, 
                     h_offset2, v_offset1,
                     h_offset2 + rect_width, v_offset1 + rect_height)
        
        canvas.coords(rect3, 
                     h_offset3, v_offset1,
                     h_offset3 + rect_width, v_offset1 + rect_height)
        
        canvas.coords(rect4, 
                     h_offset4, v_offset1,
                     h_offset4 + rect_width, v_offset1 + rect_height)
        
        # 更新文本标签位置
        canvas.coords(text1, h_offset1 + 30, v_offset1 + 30)
        canvas.coords(text2, h_offset2 + 30, v_offset1 + 30)
        canvas.coords(text3, h_offset3 + 30, v_offset1 + 30)
        canvas.coords(text4, h_offset4 + 30, v_offset1 + 30)
        
        # 更新矩形的坐标信息
        rect_coords[0] = (int(h_offset1), int(v_offset1), 
                          int(h_offset1 + rect_width), int(v_offset1 + rect_height))
        rect_coords[1] = (int(h_offset2), int(v_offset1), 
                          int(h_offset2 + rect_width), int(v_offset1 + rect_height))
        rect_coords[2] = (int(h_offset3), int(v_offset1), 
                          int(h_offset3 + rect_width), int(v_offset1 + rect_height))
        rect_coords[3] = (int(h_offset4), int(v_offset1), 
                          int(h_offset4 + rect_width), int(v_offset1 + rect_height))
        
        # 更新子框的位置
        for i in range(4):
            # 更新子框位置
            rect_coord = rect_coords[i]
            x1, y1, x2, y2 = rect_coord
            sub_x_percent, sub_y_percent, sub_width_percent, sub_height_percent = sub_rect_coords[i]
            
            # 计算子框的实际坐标
            sub_x1 = x1 + (x2 - x1) * sub_x_percent
            sub_y1 = y1 + (y2 - y1) * sub_y_percent
            sub_x2 = sub_x1 + (x2 - x1) * sub_width_percent
            sub_y2 = sub_y1 + (y2 - y1) * sub_height_percent
            
            # 更新子框位置和大小
            canvas.coords(sub_rectangles[i], sub_x1, sub_y1, sub_x2, sub_y2)
            # 更新子框标签位置
            canvas.coords(sub_text_labels[i], sub_x1 + 20, sub_y1 + 20)
            
            # 更新新子框位置
            new_sub_x_percent, new_sub_y_percent, new_sub_width_percent, new_sub_height_percent = new_sub_rect_coords[i]
            
            # 计算新子框的实际坐标
            new_sub_x1 = x1 + (x2 - x1) * new_sub_x_percent
            new_sub_y1 = y1 + (y2 - y1) * new_sub_y_percent
            new_sub_x2 = new_sub_x1 + (x2 - x1) * new_sub_width_percent
            new_sub_y2 = new_sub_y1 + (y2 - y1) * new_sub_height_percent
            
            # 更新新子框位置和大小
            canvas.coords(new_sub_rectangles[i], new_sub_x1, new_sub_y1, new_sub_x2, new_sub_y2)
            # 更新新子框标签位置
            canvas.coords(new_sub_text_labels[i], new_sub_x1 + 20, new_sub_y1 + 20)
        
        # 保存配置
        config_manager.update_config('rect_width', rect_width)
        config_manager.update_config('rect_height', rect_height)
        
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 已更新区域大小为: {rect_width}x{rect_height}像素"
        log_messages.append(log_message)
        update_log_display()
    
    # 创建子框
    sub_border_width = 2
    
    # 为每个主框创建子框
    for i, rect_coord in enumerate(rect_coords):
        x1, y1, x2, y2 = rect_coord
        sub_x_percent, sub_y_percent, sub_width_percent, sub_height_percent = sub_rect_coords[i]
        
        # 计算子框的实际坐标
        sub_x1 = x1 + (x2 - x1) * sub_x_percent
        sub_y1 = y1 + (y2 - y1) * sub_y_percent
        sub_x2 = sub_x1 + (x2 - x1) * sub_width_percent
        sub_y2 = sub_y1 + (y2 - y1) * sub_height_percent
        
        # 创建子框
        sub_rectangles[i] = canvas.create_rectangle(
            sub_x1, sub_y1, sub_x2, sub_y2,
            outline=sub_rect_custom_colors[i], width=sub_border_width, dash=(5, 5), fill='')
        
        # 创建子框标签
        sub_text_labels[i] = canvas.create_text(
            sub_x1 + 20, sub_y1 + 20,
            text=sub_rect_custom_names[i], fill=sub_rect_custom_colors[i], font=sub_text_font)
        
        # 创建新子框
        new_sub_x_percent, new_sub_y_percent, new_sub_width_percent, new_sub_height_percent = new_sub_rect_coords[i]
        
        # 计算新子框的实际坐标
        new_sub_x1 = x1 + (x2 - x1) * new_sub_x_percent
        new_sub_y1 = y1 + (y2 - y1) * new_sub_y_percent
        new_sub_x2 = new_sub_x1 + (x2 - x1) * new_sub_width_percent
        new_sub_y2 = new_sub_y1 + (y2 - y1) * new_sub_height_percent
        
        # 创建新子框，使用自定义颜色
        new_sub_rectangles[i] = canvas.create_rectangle(
            new_sub_x1, new_sub_y1, new_sub_x2, new_sub_y2,
            outline=new_sub_rect_custom_colors[i], width=2, dash=(2, 4), fill='')
        
        # 创建新子框标签
        new_sub_text_labels[i] = canvas.create_text(
            new_sub_x1 + 20, new_sub_y1 + 20,
            text=new_sub_rect_custom_names[i], fill=new_sub_rect_custom_colors[i], font=sub_text_font)
    
    # 状态变量
    status_var = tk.StringVar()
    status_var.set("准备就绪")
    folder_var = tk.StringVar()
    folder_var.set("未选择文件夹")
    rect_var = tk.StringVar()
    rect_var.set("未选择区域")
    
    # 边框显示状态
    frames_visible = False
    
    def toggle_frames():
        nonlocal frames_visible
        frames_visible = not frames_visible
        
        if frames_visible:
            overlay.deiconify()  # 显示全屏窗口
            
            # 直接更新所有子框的位置，不调用可能引起错误的函数
            for i in range(4):
                # 更新子框位置
                rect_coord = rect_coords[i]
                x1, y1, x2, y2 = rect_coord
                sub_x_percent, sub_y_percent, sub_width_percent, sub_height_percent = sub_rect_coords[i]
                
                # 计算子框的实际坐标
                sub_x1 = x1 + (x2 - x1) * sub_x_percent
                sub_y1 = y1 + (y2 - y1) * sub_y_percent
                sub_x2 = sub_x1 + (x2 - x1) * sub_width_percent
                sub_y2 = sub_y1 + (y2 - y1) * sub_height_percent
                
                # 更新子框位置和大小
                canvas.coords(sub_rectangles[i], sub_x1, sub_y1, sub_x2, sub_y2)
                # 更新子框标签位置
                canvas.coords(sub_text_labels[i], sub_x1 + 20, sub_y1 + 20)
                
                # 更新新子框位置
                new_sub_x_percent, new_sub_y_percent, new_sub_width_percent, new_sub_height_percent = new_sub_rect_coords[i]
                
                # 计算新子框的实际坐标
                new_sub_x1 = x1 + (x2 - x1) * new_sub_x_percent
                new_sub_y1 = y1 + (y2 - y1) * new_sub_y_percent
                new_sub_x2 = new_sub_x1 + (x2 - x1) * new_sub_width_percent
                new_sub_y2 = new_sub_y1 + (y2 - y1) * new_sub_height_percent
                
                # 更新新子框位置和大小
                canvas.coords(new_sub_rectangles[i], new_sub_x1, new_sub_y1, new_sub_x2, new_sub_y2)
                # 更新新子框标签位置
                canvas.coords(new_sub_text_labels[i], new_sub_x1 + 20, new_sub_y1 + 20)
        else:
            overlay.withdraw()  # 隐藏全屏窗口
        
        status_var.set("边框已" + ("显示" if frames_visible else "隐藏"))
    
    # 允许点击穿透全屏窗口
    def set_click_through():
        hwnd = win32gui.FindWindow(None, "")
        # 设置窗口样式为点击穿透
        win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE,
                            win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE) | win32con.WS_EX_TRANSPARENT)
    
    # 在窗口显示后设置点击穿透
    overlay.bind("<Map>", lambda e: set_click_through())
    
    # 获取目标应用进程列表
    def get_wow_processes():
        wow_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 使用更通用的进程名检测方式
                if proc.info['name'].lower() in ['wowclassic.exe', 'wow.exe', 'game.exe']:
                    wow_processes.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        return wow_processes
    
    # 选择图像文件夹
    def select_image_folder():
        global image_folder, template_images
        folder = filedialog.askdirectory(title="选择包含模板图像的文件夹")
        if folder:
            image_folder = folder
            folder_var.set(f"已选择: {os.path.basename(folder)}")
            # 加载文件夹中的所有图像作为模板
            template_images = []
            templates_listbox.delete(0, tk.END)  # 清空列表框
            
            # 检查文件夹是否存在
            if not os.path.exists(folder):
                messagebox.showwarning("警告", "所选文件夹不存在！")
                return
                
            try:
                image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
                if not image_files:
                    messagebox.showwarning("警告", "所选文件夹中没有找到图像文件！")
                    return
            except PermissionError:
                messagebox.showwarning("警告", "无法访问所选文件夹，权限被拒绝！")
                return
            except Exception as e:
                messagebox.showwarning("警告", f"读取文件夹时出错: {str(e)}")
                return
            
            # 记录加载结果
            loaded_count = 0
            skipped_count = 0
            error_count = 0
            
            # 加载前50个图像作为模板
            for img_file in image_files[:50]:
                try:
                    img_path = os.path.join(folder, img_file)
                    template = cv2.imread(img_path)
                    
                    if template is None:
                        log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 无法加载图像 {img_file}，可能是不支持的格式或损坏"
                        log_messages.append(log_message)
                        update_log_display()
                        skipped_count += 1
                        continue
                        
                    # 检查模板大小
                    h, w = template.shape[:2]
                    if h > rect_height or w > rect_width:
                        # 如果模板太大，记录并跳过
                        log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 模板 {img_file} ({w}x{h}) 可能过大，已跳过"
                        log_messages.append(log_message)
                        update_log_display()
                        skipped_count += 1
                        continue
                        
                    template_images.append((img_file, template))
                    # 在UI中显示已加载的模板图像名称
                    templates_listbox.insert(tk.END, img_file)
                    loaded_count += 1
                except Exception as e:
                    log_message = f"[{time.strftime('%H:%M:%S')}] 加载图像 {img_file} 时出错: {str(e)}"
                    log_messages.append(log_message)
                    update_log_display()
                    error_count += 1
            
            if template_images:
                result_msg = f"已加载 {loaded_count} 个模板图像"
                if skipped_count > 0 or error_count > 0:
                    result_msg += f"，跳过 {skipped_count} 个不适合的模板"
                    if error_count > 0:
                        result_msg += f"，{error_count} 个加载出错"
                messagebox.showinfo("成功", result_msg)
                # 保存图像文件夹路径到配置
                config_manager.update_config('image_folder', folder)
            else:
                messagebox.showwarning("警告", "无法加载任何模板图像！")
                # 记录详细日志
                log_message = f"[{time.strftime('%H:%M:%S')}] 无法从 {folder} 加载任何模板图像"
                log_messages.append(log_message)
                update_log_display()
    
    # 选择新子框图像文件夹
    def select_new_image_folder():
        # 使用当前选择的新子框索引
        selected_new_subrect_index = selected_new_subrect.get()
            
        folder = filedialog.askdirectory(title=f"选择新子框 {selected_new_subrect_index+1} 模板图像文件夹")
        if folder:
            new_sub_image_folders[selected_new_subrect_index] = folder
            # 更新UI显示
            new_image_folder_var.set(folder)
            
            # 清空当前模板
            new_sub_template_images[selected_new_subrect_index] = []
            
            # 加载文件夹中的图像作为模板
            image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
            if not image_files:
                messagebox.showwarning("警告", f"所选文件夹中没有找到图像文件！")
                return
                
            # 记录加载结果
            loaded_count = 0
            skipped_count = 0
            
            # 计算这个新子框的最大可能尺寸(相对于主矩形)
            i = selected_new_subrect_index
            new_sub_x_percent, new_sub_y_percent, new_sub_width_percent, new_sub_height_percent = new_sub_rect_coords[i]
            max_width = int(rect_width * new_sub_width_percent)
            max_height = int(rect_height * new_sub_height_percent)
                
            # 最多加载50张图片
            for img_file in image_files[:50]:
                try:
                    img_path = os.path.join(folder, img_file)
                    template = cv2.imread(img_path)
                    
                    if template is not None:
                        # 检查模板大小
                        h, w = template.shape[:2]
                        if h > max_height or w > max_width:
                            # 如果模板太大，记录并跳过
                            log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 子框模板 {img_file} ({w}x{h}) 可能过大(最大允许{max_width}x{max_height})，已跳过"
                            log_messages.append(log_message)
                            update_log_display()
                            skipped_count += 1
                            continue
                            
                        # 添加模板
                        new_sub_template_images[selected_new_subrect_index].append((img_file, template))
                        loaded_count += 1
                except Exception as e:
                    log_message = f"[{time.strftime('%H:%M:%S')}] 加载图像 {img_file} 时出错: {str(e)}"
                    log_messages.append(log_message)
                    update_log_display()
                    skipped_count += 1
                    
            if loaded_count > 0:
                result_msg = f"已为新子框 {selected_new_subrect_index+1} 加载 {loaded_count} 个模板图像"
                if skipped_count > 0:
                    result_msg += f"，跳过 {skipped_count} 个不适合的模板"
                messagebox.showinfo("成功", result_msg)
            else:
                messagebox.showwarning("警告", "无法加载任何模板图像！")
                
            # 保存到配置
            config_manager.update_config(f'new_sub_image_folder_{selected_new_subrect_index}', folder)
    
    # 模拟按S键
    def press_s_key(pid=None):
        if pid:
            try:
                # 使用pyautogui模拟按键，发送到指定进程
                # 注意：pyautogui默认不能指定进程，这里使用win32api实现
                hwnd = None
                # 查找进程对应的窗口句柄
                def callback(h, extra):
                    nonlocal hwnd
                    try:
                        _, process_id = win32process.GetWindowThreadProcessId(h)
                        if process_id == pid and win32gui.IsWindowVisible(h):
                            hwnd = h
                            return False
                    except:
                        pass
                    return True
                
                win32gui.EnumWindows(callback, None)
                
                if hwnd:
                    # 添加随机延迟，模拟人类按键的不规则性
                    pre_delay = random.uniform(0.05, 0.15)  # 按键前的随机延迟 50-150ms
                    time.sleep(pre_delay)
                    
                    # S键的扫描码是0x1F (31)
                    scan_code = 0x1F
                    
                    # 构建lParam参数
                    # bit 0-15: 重复次数，通常为1
                    # bit 16-23: 扫描码
                    # bit 24: 扩展键标志 (0表示非扩展键)
                    # bit 25: 保留
                    # bit 26-27: 上下文代码 (通常为0)
                    # bit 28: 前一个键状态 (0表示键之前未按下)
                    # bit 29: 转换状态 (通常为0)
                    # bit 30: 始终为0
                    # bit 31: 0表示按下，1表示释放
                    
                    # 按键按下lParam
                    key_down_lparam = (0x00000001 | (scan_code << 16))
                    
                    # 按键释放lParam (设置bit 31)
                    key_up_lparam = key_down_lparam | (1 << 31)
                    
                    # 发送按键消息
                    win32api.PostMessage(hwnd, win32con.WM_KEYDOWN, 0x53, key_down_lparam)  # S键的虚拟键码是0x53
                    
                    # 添加随机的按键持续时间，模拟真实按键
                    key_hold_time = random.uniform(0.07, 0.18)  # 70-180毫秒的按键时间
                    time.sleep(key_hold_time)
                    
                    win32api.PostMessage(hwnd, win32con.WM_KEYUP, 0x53, key_up_lparam)
                    
                    # 偶尔添加一个额外的微小延迟，进一步增加随机性
                    if random.random() < 0.3:  # 30%的概率
                        time.sleep(random.uniform(0.01, 0.05))
                    
                    status_var.set(f"已向PID {pid}发送按键信号")
                else:
                    status_var.set(f"无法找到PID {pid}对应的窗口")
            except Exception as e:
                status_var.set(f"发送按键时出错: {e}")
        else:
            # 如果没有指定PID，则全局按S键，同样添加随机性
            # 使用pyautogui的更自然按键方式
            pre_delay = random.uniform(0.05, 0.15)  # 按键前的随机延迟 50-150ms
            time.sleep(pre_delay)
            
            key_duration = random.uniform(0.07, 0.18)  # 随机按键持续时间 70-180ms
            pyautogui.keyDown('s')
            time.sleep(key_duration)
            pyautogui.keyUp('s')
            status_var.set("已发送按键信号")
    
    # 图像识别函数
    def recognize_image():
        global selected_rect_index, area_in_cooldown, last_trigger_time, new_sub_area_in_cooldown, new_sub_last_trigger_time, sub_rect_paused

        # 添加资源管理和异常处理
        screenshots = []
        new_sub_screenshots = []

        try:
            if not template_images:
                # 在监控线程中不显示消息框，只记录日志
                if threading.current_thread() != threading.main_thread():
                    log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 未选择模板图像文件夹"
                    log_messages.append(log_message)
                    try:
                        update_log_display()
                    except:
                        pass
                    return
                else:
                    messagebox.showwarning("警告", "请先选择包含模板图像的文件夹！")
                    return

            # 检查是否至少有一个区域关联了进程
            has_process = False
            for pid in rect_pid_mapping.values():
                if pid is not None:
                    has_process = True
                    break

            if not has_process:
                # 在监控线程中不显示消息框，只记录日志
                if threading.current_thread() != threading.main_thread():
                    log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 未关联任何进程"
                    log_messages.append(log_message)
                    try:
                        update_log_display()
                    except:
                        pass
                    return
                else:
                    messagebox.showwarning("警告", "请先为区域关联进程！")
                    return
            
            # 当前时间
            current_time = time.time()
            
            # 更新冷却状态
            for i in range(4):
                if area_in_cooldown[i] and current_time - last_trigger_time[i] >= actual_cooldown_time[i]:
                    area_in_cooldown[i] = False
                    # 冷却结束，恢复原来的边框颜色
                    canvas.itemconfig(rectangles[i], outline=rect_custom_colors[i], width=border_width)
                    log_message = f"[{time.strftime('%H:%M:%S')}] {rect_custom_names[i]} PID: {rect_pid_mapping[i]} 冷却结束，实际冷却时间: {actual_cooldown_time[i]}秒"
                    log_messages.append(log_message)
                    update_log_display()
                
                # 更新新子框的禁止操作状态
                if new_sub_area_in_cooldown[i] and current_time - new_sub_last_trigger_time[i] >= new_sub_actual_cooldown_time[i]:
                    new_sub_area_in_cooldown[i] = False
                    # 禁止操作结束，恢复原来的边框颜色
                    canvas.itemconfig(new_sub_rectangles[i], outline=new_sub_rect_custom_colors[i], width=2)
                    
                    # 如果主区域不在冷却状态，也恢复主区域的颜色
                    if not area_in_cooldown[i]:
                        canvas.itemconfig(rectangles[i], outline=rect_custom_colors[i], width=border_width)
                    
                    # 恢复子框识别状态
                    sub_rect_paused[i] = False
                    # 不再改变子框颜色
                    
                    log_message = f"[{time.strftime('%H:%M:%S')}] {new_sub_rect_custom_names[i]} 禁止操作结束，实际冷却时间: {new_sub_actual_cooldown_time[i]}秒，{rect_custom_names[i]}可以再次触发S键，子框恢复识别"
                    log_messages.append(log_message)
                    update_log_display()

            # 截取四个矩形区域的屏幕
            # 增强防检测
            if anti_detection_enabled:
                enhanced_anti_detection()
            
            for i, (x1, y1, x2, y2) in enumerate(rect_coords):
                # 只处理有关联PID的区域
                if rect_pid_mapping[i] is not None:
                    try:
                        # 计算子框的实际坐标
                        sub_x_percent, sub_y_percent, sub_width_percent, sub_height_percent = sub_rect_coords[i]
                        sub_x1 = int(x1 + (x2 - x1) * sub_x_percent)
                        sub_y1 = int(y1 + (y2 - y1) * sub_y_percent)
                        sub_width = int((x2 - x1) * sub_width_percent)
                        sub_height = int((y2 - y1) * sub_height_percent)
                        
                        # 验证坐标有效性
                        if sub_width <= 0 or sub_height <= 0:
                            continue
                        
                        # 截取子框区域 - 使用安全截图函数
                        if anti_detection_enabled and randomize_screenshot:
                            screenshot = secure_screenshot(region=(sub_x1, sub_y1, sub_width, sub_height))
                        else:
                            screenshot = pyautogui.screenshot(region=(sub_x1, sub_y1, sub_width, sub_height))
                        
                        if screenshot is None:
                            continue
                        
                        # 转换为OpenCV格式
                        screenshot_array = np.array(screenshot)
                        screenshot_cv = cv2.cvtColor(screenshot_array, cv2.COLOR_RGB2BGR)
                        screenshots.append((i, screenshot_cv))
                        
                        # 立即释放PIL图像对象
                        del screenshot
                        del screenshot_array
                        
                        # 截取新子框区域
                        new_sub_x_percent, new_sub_y_percent, new_sub_width_percent, new_sub_height_percent = new_sub_rect_coords[i]
                        new_sub_x1 = int(x1 + (x2 - x1) * new_sub_x_percent)
                        new_sub_y1 = int(y1 + (y2 - y1) * new_sub_y_percent)
                        new_sub_width = int((x2 - x1) * new_sub_width_percent)
                        new_sub_height = int((y2 - y1) * new_sub_height_percent)
                        
                        # 验证坐标有效性
                        if new_sub_width <= 0 or new_sub_height <= 0:
                            continue
                        
                        # 截取新子框区域 - 使用安全截图函数
                        if anti_detection_enabled and randomize_screenshot:
                            new_sub_screenshot = secure_screenshot(region=(new_sub_x1, new_sub_y1, new_sub_width, new_sub_height))
                        else:
                            new_sub_screenshot = pyautogui.screenshot(region=(new_sub_x1, new_sub_y1, new_sub_width, new_sub_height))
                        
                        if new_sub_screenshot is None:
                            continue
                        
                        # 转换为OpenCV格式
                        new_sub_screenshot_array = np.array(new_sub_screenshot)
                        new_sub_screenshot_cv = cv2.cvtColor(new_sub_screenshot_array, cv2.COLOR_RGB2BGR)
                        new_sub_screenshots.append((i, new_sub_screenshot_cv))
                        
                        # 立即释放PIL图像对象
                        del new_sub_screenshot
                        del new_sub_screenshot_array
                        
                        # 随机延迟 - 降低检测风险
                        if anti_detection_enabled and randomize_timing:
                            time.sleep(random.uniform(0.001, 0.005))
                            
                    except Exception as e:
                        log_message = f"[{time.strftime('%H:%M:%S')}] 截图区域 {i+1} 时出错: {str(e)}"
                        log_messages.append(log_message)
                        try:
                            update_log_display()
                        except:
                            print(log_message)
                        continue

            # 检查新子框中是否有匹配的图像
        new_sub_matched_areas = []
        
        for i, new_sub_screenshot in new_sub_screenshots:
            # 如果该区域在冷却中，则跳过
            if new_sub_area_in_cooldown[i]:
                continue
            
            # 如果该新子框没有模板图像，则跳过
            if not new_sub_template_images[i]:
                continue
            
            # 记录每个模板的匹配结果
            match_results = []
            
            # 检查所有模板图像
            for template_name, template in new_sub_template_images[i]:
                # 检查模板大小是否大于图像
                h, w = new_sub_screenshot.shape[:2]
                template_h, template_w = template.shape[:2]
                
                # 如果模板大于图像，则记录日志并跳过该模板
                if template_h > h or template_w > w:
                    log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 模板 {template_name} ({template_w}x{template_h}) 大于新子框截图 ({w}x{h})，已跳过"
                    log_messages.append(log_message)
                    update_log_display()
                    continue
                
                # 使用安全的图像识别方法
                try:
                    if anti_detection_enabled and img_recognize_method != 'normal':
                        result = secure_image_recognition(new_sub_screenshot, template)
                    else:
                        # 使用OpenCV的模板匹配
                        result = cv2.matchTemplate(new_sub_screenshot, template, cv2.TM_CCOEFF_NORMED)
                    
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                    
                    # 记录匹配结果
                    match_results.append((template_name, max_val))
                    
                    # 添加日志记录
                    log_message = f"[{time.strftime('%H:%M:%S')}] 新子框 N.{i+1} 图像 {template_name} 匹配度: {max_val:.2f}"
                    log_messages.append(log_message)
                    update_log_display()
                except Exception as e:
                    log_message = f"[{time.strftime('%H:%M:%S')}] 错误: 新子框 N.{i+1} 图像 {template_name} 匹配失败: {str(e)}"
                    log_messages.append(log_message)
                    update_log_display()
                
                # 随机延迟 - 降低检测风险
                if anti_detection_enabled and randomize_timing:
                    time.sleep(random.uniform(0.001, 0.003))
            
            # 检查是否有有效的匹配结果
            if not match_results:
                log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 新子框 N.{i+1} 没有有效的模板图像可匹配"
                log_messages.append(log_message)
                update_log_display()
                continue
            
            # 检查是否有任何一个模板达到阈值
            matched = False
            for _, match_val in match_results:
                if match_val >= new_sub_recognition_thresholds[i]:
                    matched = True
                    break
            
            # 如果有任何一个模板匹配成功
            if matched and match_results:  # 确保有模板图像
                # 计算最高匹配度
                max_match = max(val for _, val in match_results)
                
                # 不再高亮显示匹配的新子框，保持原来的颜色
                # 使用原始的边框宽度和样式
                canvas.itemconfig(new_sub_rectangles[i], outline=new_sub_rect_custom_colors[i], width=2)
                
                # 记录新子框识别到图像，用于后续处理
                new_sub_area_in_cooldown[i] = True
                new_sub_last_trigger_time[i] = current_time
                
                # 计算随机冷却时间
                random_cooldown = new_sub_cooldown_times[i]
                if new_sub_cooldown_random_range > 0:
                    # 在设定的范围内添加随机值
                    random_cooldown += random.randint(-new_sub_cooldown_random_range, new_sub_cooldown_random_range)
                    # 确保冷却时间不小于1秒
                    random_cooldown = max(1, random_cooldown)
                
                # 存储实际计算出的冷却时间
                new_sub_actual_cooldown_time[i] = random_cooldown
                actual_cooldown_time[i] = random_cooldown  # 同时更新主区域的实际冷却时间
                
                # 直接设置主区域进入冷却状态
                area_in_cooldown[i] = True
                # 使用当前时间作为冷却开始时间，而不是减去cooldown_time
                last_trigger_time[i] = current_time
                
                # 设置主框框为棕色，表示新子框识别到图像进入冷却状态
                canvas.itemconfig(rectangles[i], outline="#8B4513", width=border_width)
                
                # 设置子框暂停识别状态
                sub_rect_paused[i] = True
                # 不再改变子框颜色
                
                # 添加日志记录
                log_message = f"[{time.strftime('%H:%M:%S')}] {new_sub_rect_custom_names[i]} 识别到图像，{rect_custom_names[i]}进入冷却状态，子框暂停识别，实际冷却时间为 {random_cooldown} 秒，最高匹配度: {max_match:.2f}"
                log_messages.append(log_message)
                update_log_display()
                
                # 记录匹配的区域
                if i not in new_sub_matched_areas:
                    new_sub_matched_areas.append(i)
                
                # 立即返回，不执行后续的主区域识别
                # 这样可以确保在新子框识别到图像后，不会在同一次循环中执行主区域的识别
                return

            # 在每个区域中查找模板图像
            for i, screenshot in screenshots:
                # 如果该区域在冷却中，则跳过
                if area_in_cooldown[i]:
                    continue

                # 如果该子框暂停识别，则跳过
                if sub_rect_paused[i]:
                    continue

                # 记录每个模板的匹配结果
                match_results = []

                # 获取截图尺寸
                h, w = screenshot.shape[:2]

                # 检查所有模板图像
                for template_name, template in template_images:
                    # 检查模板大小是否大于图像
                    template_h, template_w = template.shape[:2]

                    # 如果模板大于图像，则记录日志并跳过该模板
                    if template_h > h or template_w > w:
                        log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 模板 {template_name} ({template_w}x{template_h}) 大于截图 ({w}x{h})，已跳过"
                        log_messages.append(log_message)
                        update_log_display()
                        continue

                    # 使用安全的图像识别方法
                    try:
                        if anti_detection_enabled and img_recognize_method != 'normal':
                            result = secure_image_recognition(screenshot, template)
                        else:
                            # 使用OpenCV的模板匹配
                            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)

                        _, max_val, _, _ = cv2.minMaxLoc(result)

                        # 记录匹配结果
                        match_results.append((template_name, max_val))

                        # 添加日志记录
                        log_message = f"[{time.strftime('%H:%M:%S')}] 区域 {i+1} 图像 {template_name} 匹配度: {max_val:.2f}"
                        log_messages.append(log_message)
                        update_log_display()
                    except Exception as e:
                        log_message = f"[{time.strftime('%H:%M:%S')}] 错误: 区域 {i+1} 图像 {template_name} 匹配失败: {str(e)}"
                        log_messages.append(log_message)
                        update_log_display()

                    # 随机延迟 - 降低检测风险
                    if anti_detection_enabled and randomize_timing:
                        time.sleep(random.uniform(0.001, 0.003))

                # 检查是否有有效的匹配结果
                if not match_results:
                    log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 区域 {i+1} 没有有效的模板图像可匹配"
                    log_messages.append(log_message)
                    update_log_display()
                    continue

                # 检查是否有任一模板达到阈值
                any_matched = False
                highest_match = 0
                best_template = None

                for template_name, match_val in match_results:
                    if match_val > highest_match:
                        highest_match = match_val
                        best_template = template_name

                    # 根据阈值模式进行识别判断
                    if threshold_mode == 'range':
                        # 范围阈值模式：只有在范围内才算识别成功
                        if sub_threshold_min <= match_val <= sub_threshold_max:
                            any_matched = True
                    elif threshold_mode == 'fixed':
                        # 固定阈值模式：大于或等于阈值才算识别成功
                        if match_val >= recognition_threshold:
                            any_matched = True

                # 如果有任一模板匹配成功
                if any_matched and match_results:  # 确保有模板图像
                    selected_rect_index = i
                    rect_var.set(f"已选择区域: {i+1}")

                    # 计算平均匹配度
                    avg_match = sum(val for _, val in match_results) / len(match_results)

                    # 高亮显示匹配的矩形 - 不在这里设置颜色，而是根据识别来源设置
                    for j, rect in enumerate(rectangles):
                        if j != i:  # 只重置非匹配区域
                            canvas.itemconfig(rect, outline=rect_custom_colors[j], width=border_width)

                    # 确定要使用的进程ID
                    target_pid = rect_pid_mapping[i]  # 使用区域关联的进程

                    # 检查该区域的新子框是否匹配，如果是则根据当前冷却状态处理冷却时间
                    # 只有在子框框识别到图像时才修改冷却时间
                    if i in new_sub_matched_areas:
                        # 计算随机冷却时间
                        random_cooldown = new_sub_cooldown_times[i]
                        if new_sub_cooldown_random_range > 0:
                            # 在设定的范围内添加随机值
                            random_cooldown += random.randint(-new_sub_cooldown_random_range, new_sub_cooldown_random_range)
                            # 确保冷却时间不小于1秒
                            random_cooldown = max(1, random_cooldown)

                        # 判断当前是否在冷却中
                        if area_in_cooldown[i]:
                            # 存储实际计算出的冷却时间
                            actual_cooldown_time[i] = random_cooldown

                            # 如果在冷却中，则重置冷却时间为当前时间
                            last_trigger_time[i] = current_time

                            # 添加日志记录
                            log_message = f"[{time.strftime('%H:%M:%S')}] {rect_custom_names[i]} PID: {target_pid} 已在冷却中，{new_sub_rect_custom_names[i]}重置冷却时间为{random_cooldown}秒"
                            log_messages.append(log_message)
                            update_log_display()

                            if threading.current_thread() == threading.main_thread():
                                status_var.set(f"在{rect_custom_names[i]}中图像匹配成功，{rect_custom_names[i]}已在冷却中，{new_sub_rect_custom_names[i]}重置冷却时间为{random_cooldown}秒")
                        else:
                            # 如果不在冷却中，则进入冷却状态，冷却时间为直接使用当前时间
                            area_in_cooldown[i] = True
                            last_trigger_time[i] = current_time

                            # 设置框框为棕色，表示新子框识别到图像进入冷却状态
                            canvas.itemconfig(rectangles[i], outline="#8B4513", width=border_width)

                            if threading.current_thread() == threading.main_thread():
                                status_var.set(f"在{rect_custom_names[i]}中图像匹配成功，{new_sub_rect_custom_names[i]}设置冷却时间为{random_cooldown}秒")

                            # 添加日志记录
                            log_message = f"[{time.strftime('%H:%M:%S')}] {rect_custom_names[i]} PID: {target_pid} 匹配成功，{new_sub_rect_custom_names[i]}设置冷却时间为{random_cooldown}秒"
                            log_messages.append(log_message)
                            update_log_display()

                        # 清除新子框的匹配状态，以便下次可以再次触发
                        if i in new_sub_matched_areas:
                            new_sub_matched_areas.remove(i)
                        # 注意：保持新子框的冷却状态，不要重置为False
                        # 这样可以在UI上显示冷却状态，但不会影响实际的冷却时间
                    # 如果找到匹配，且新子框没有阻止，则按S键
                    elif target_pid:
                        # 使用更安全的方式按S键
                        if anti_detection_enabled:
                            # 添加随机延迟前后的随机延迟
                            if randomize_timing:
                                time.sleep(random.uniform(0.01, 0.03))

                        press_s_key(target_pid)
                        if threading.current_thread() == threading.main_thread():
                            status_var.set(f"在{rect_custom_names[i]}中图像匹配成功，已向PID {target_pid} 发送S键")

                        # 计算随机冷却时间
                        random_cooldown = cooldown_time
                        if cooldown_random_range > 0:
                            # 在设定的范围内添加随机值
                            random_cooldown += random.randint(-cooldown_random_range, cooldown_random_range)
                            # 确保冷却时间不小于1秒
                            random_cooldown = max(1, random_cooldown)

                        # 存储实际计算出的冷却时间
                        actual_cooldown_time[i] = random_cooldown

                        # 设置冷却时间，直接使用当前时间，不再做额外计算
                        area_in_cooldown[i] = True
                        last_trigger_time[i] = current_time

                        # 设置框框为红色，表示子框识别到图像进入冷却状态
                        canvas.itemconfig(rectangles[i], outline="#FF0000", width=border_width)

                        # 添加日志记录
                        log_message = f"[{time.strftime('%H:%M:%S')}] {rect_custom_names[i]} PID: {target_pid} 进入冷却状态 {random_cooldown} 秒，最匹配的图像: {best_template}，匹配度: {highest_match:.2f}"
                        log_messages.append(log_message)
                        update_log_display()

                    return

            # 如果没有找到匹配
            if threading.current_thread() == threading.main_thread():
                status_var.set("未找到匹配的图像")
            # 添加日志记录
            log_message = f"[{time.strftime('%H:%M:%S')}] 未在任何区域找到匹配的图像"
            log_messages.append(log_message)
            update_log_display()

        except Exception as e:
            # 全局异常处理
            log_message = f"[{time.strftime('%H:%M:%S')}] 图像识别过程出错: {str(e)}"
            log_messages.append(log_message)
            try:
                update_log_display()
                if threading.current_thread() == threading.main_thread():
                    status_var.set(f"识别出错: {e}")
            except:
                print(log_message)

        finally:
            # 确保资源被释放
            try:
                # 清理截图数据
                for _, screenshot in screenshots:
                    if screenshot is not None:
                        del screenshot
                for _, new_sub_screenshot in new_sub_screenshots:
                    if new_sub_screenshot is not None:
                        del new_sub_screenshot

                # 强制垃圾回收
                import gc
                gc.collect()
            except:
                pass
    
    # 开始/停止监控
    def toggle_monitoring():
        global monitoring_active, monitoring_thread

        if monitoring_active:
            monitoring_active = False
            # 更安全的线程停止机制
            if monitoring_thread and monitoring_thread.is_alive():
                log_message = f"[{time.strftime('%H:%M:%S')}] 正在停止监控线程..."
                log_messages.append(log_message)
                update_log_display()

                # 等待线程结束，增加超时时间
                monitoring_thread.join(timeout=3.0)

                # 如果线程仍然活跃，记录警告
                if monitoring_thread.is_alive():
                    log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 监控线程未能在3秒内正常退出"
                    log_messages.append(log_message)
                    update_log_display()
                else:
                    log_message = f"[{time.strftime('%H:%M:%S')}] 监控线程已正常退出"
                    log_messages.append(log_message)
                    update_log_display()

            monitor_button.config(text="开始监控")
            status_var.set("监控已停止")
        else:
            if not template_images:
                messagebox.showwarning("警告", "请先选择包含模板图像的文件夹！")
                return

            # 检查是否至少有一个区域关联了进程
            has_process = False
            for pid in rect_pid_mapping.values():
                if pid is not None:
                    has_process = True
                    break

            if not has_process:
                messagebox.showwarning("警告", "请先为至少一个区域关联进程！")
                return

            # 确保之前的线程已经完全停止
            if monitoring_thread and monitoring_thread.is_alive():
                log_message = f"[{time.strftime('%H:%M:%S')}] 等待之前的监控线程完全停止..."
                log_messages.append(log_message)
                update_log_display()
                monitoring_thread.join(timeout=5.0)

            monitoring_active = True
            monitor_button.config(text="停止监控")
            status_var.set("监控中...")

            # 启动监控线程
            monitoring_thread = threading.Thread(target=monitoring_task, name="MonitoringThread")
            monitoring_thread.daemon = True
            monitoring_thread.start()

            log_message = f"[{time.strftime('%H:%M:%S')}] 监控线程已启动"
            log_messages.append(log_message)
            update_log_display()
    
    # 监控任务
    def monitoring_task():
        global monitoring_active
        
        # 初始化随机种子
        random.seed(time.time())
        
        # 记录上次识别时间，用于实现不规则的识别间隔
        last_recognition_time = time.time()
        last_memory_clean = time.time()
        error_count = 0  # 错误计数器
        max_errors = 3  # 减少最大连续错误数，更快停止有问题的监控
        
        # 添加资源管理器
        screenshot_cache = []
        template_cache = []
        
        try:
            while monitoring_active:
                try:
                    # 检查监控状态
                    if not monitoring_active:
                        break
                    
                    # 获取当前时间
                    current_time = time.time()
                    
                    # 更频繁的内存清理，防止内存泄漏
                    if current_time - last_memory_clean > 5:  # 每5秒清理一次，更频繁
                        try:
                            # 清理缓存
                            screenshot_cache.clear()
                            template_cache.clear()
                            
                            import gc
                            # 强制垃圾回收
                            gc.collect()
                            last_memory_clean = current_time
                            
                            # 强制清理内存工作集
                            if sys.platform == 'win32':
                                try:
                                    current_process = ctypes.windll.kernel32.GetCurrentProcess()
                                    ctypes.windll.psapi.EmptyWorkingSet(current_process)
                                except:
                                    pass
                            
                            # 记录内存清理
                            log_message = f"[{time.strftime('%H:%M:%S')}] 定期内存清理完成"
                            log_messages.append(log_message)
                            try:
                                update_log_display()
                            except:
                                pass
                        except Exception as e:
                            print(f"内存清理失败: {e}")
                    
                    # 如果启用了防检测功能，添加随机延迟
                    if anti_detection_enabled and randomize_timing:
                        # 增加间隔时间，减少CPU占用和崩溃风险
                        base_interval = random.uniform(0.2, 0.3)  # 增加基础间隔
                        jitter = random.uniform(-0.02, 0.02)  # 添加±20毫秒的抖动
                        recognition_interval = base_interval + jitter
                        
                        # 偶尔添加一个较长的暂停（约1%的概率），模拟人类操作的不规则性
                        if random.random() < 0.01:
                            extra_pause = random.uniform(0.5, 1.0)  # 额外增加500-1000毫秒
                            time.sleep(extra_pause)
                    else:
                        # 不启用防检测时使用固定间隔，增加到200毫秒减少CPU占用
                        recognition_interval = 0.2  # 200毫秒
                    
                    # 执行图像识别，添加全局异常处理和内存保护
                    try:
                        # 在执行识别前检查内存使用情况
                        try:
                            import psutil
                            process = psutil.Process()
                            memory_percent = process.memory_percent()
                            
                            # 如果内存使用超过50%，强制清理（降低阈值）
                            if memory_percent > 50:
                                # 清理缓存
                                screenshot_cache.clear()
                                template_cache.clear()
                                
                                import gc
                                gc.collect()
                                time.sleep(0.1)  # 给系统时间清理
                                
                                # 强制清理内存工作集
                                if sys.platform == 'win32':
                                    try:
                                        current_process = ctypes.windll.kernel32.GetCurrentProcess()
                                        ctypes.windll.psapi.EmptyWorkingSet(current_process)
                                    except:
                                        pass
                                
                                log_message = f"[{time.strftime('%H:%M:%S')}] 内存使用过高({memory_percent:.1f}%)，已清理"
                                log_messages.append(log_message)
                                try:
                                    update_log_display()
                                except:
                                    pass
                        except:
                            pass  # 如果psutil不可用，忽略内存检查
                        
                        # 执行图像识别，使用try-catch包装
                        try:
                            recognize_image()
                            error_count = 0  # 重置错误计数器
                        except Exception as recognition_error:
                            # 图像识别特定错误处理
                            error_count += 1
                            error_msg = str(recognition_error)
                            log_message = f"[{time.strftime('%H:%M:%S')}] 图像识别错误 ({error_count}/{max_errors}): {error_msg}"
                            log_messages.append(log_message)
                            try:
                                update_log_display()
                            except:
                                print(log_message)
                            
                            # 清理可能的残留资源
                            try:
                                screenshot_cache.clear()
                                template_cache.clear()
                                import gc
                                gc.collect()
                                # 强制清理内存工作集
                                if sys.platform == 'win32':
                                    try:
                                        current_process = ctypes.windll.kernel32.GetCurrentProcess()
                                        ctypes.windll.psapi.EmptyWorkingSet(current_process)
                                    except:
                                        pass
                            except:
                                pass
                            
                            # 如果是内存相关错误，增加延迟
                            if "memory" in error_msg.lower() or "out of" in error_msg.lower():
                                time.sleep(5.0)
                            else:
                                time.sleep(2.0)
                        
                    except MemoryError:
                        # 内存不足错误的特殊处理
                        error_count += 1
                        log_message = f"[{time.strftime('%H:%M:%S')}] 内存不足错误 ({error_count}/{max_errors})，正在清理内存"
                        log_messages.append(log_message)
                        try:
                            update_log_display()
                        except:
                            print(log_message)
                        
                        # 强制清理内存
                        try:
                            screenshot_cache.clear()
                            template_cache.clear()
                            import gc
                            gc.collect()
                            # 强制清理内存工作集
                            if sys.platform == 'win32':
                                try:
                                    current_process = ctypes.windll.kernel32.GetCurrentProcess()
                                    ctypes.windll.psapi.EmptyWorkingSet(current_process)
                                except:
                                    pass
                            time.sleep(5.0)  # 给系统更多时间清理
                        except:
                            pass
                        
                        # 如果连续内存错误过多，停止监控
                        if error_count >= 2:  # 内存错误容忍度更低
                            log_message = f"[{time.strftime('%H:%M:%S')}] 连续内存错误过多，停止监控"
                            log_messages.append(log_message)
                            try:
                                update_log_display()
                                status_var.set("监控因内存不足而停止")
                            except:
                                print(log_message)
                            break
                        
                        # 内存错误后增加更长的延迟
                        time.sleep(10.0)
                        
                    except Exception as e:
                        error_count += 1
                        error_msg = str(e)
                        log_message = f"[{time.strftime('%H:%M:%S')}] 监控任务出错 ({error_count}/{max_errors}): {error_msg}"
                        log_messages.append(log_message)
                        try:
                            update_log_display()
                        except:
                            print(log_message)
                        
                        # 清理可能的残留资源
                        try:
                            screenshot_cache.clear()
                            template_cache.clear()
                            import gc
                            gc.collect()
                            # 强制清理内存工作集
                            if sys.platform == 'win32':
                                try:
                                    current_process = ctypes.windll.kernel32.GetCurrentProcess()
                                    ctypes.windll.psapi.EmptyWorkingSet(current_process)
                                except:
                                    pass
                        except:
                            pass
                        
                        # 如果连续错误过多，停止监控
                        if error_count >= max_errors:
                            log_message = f"[{time.strftime('%H:%M:%S')}] 连续错误过多，停止监控"
                            log_messages.append(log_message)
                            try:
                                update_log_display()
                                status_var.set("监控因连续错误而停止")
                            except:
                                print(log_message)
                            break
                        
                        # 添加短暂延迟，避免连续错误导致CPU占用过高
                        time.sleep(min(3.0 * error_count, 10.0))  # 递增延迟，最多10秒
                    
                    # 使用动态计算的间隔时间
                    if monitoring_active:  # 再次检查状态
                        time.sleep(recognition_interval)
                    
                    # 每隔一段时间（约120秒）稍微改变一下识别频率，进一步增加行为随机性
                    if anti_detection_enabled and randomize_timing and current_time - last_recognition_time > 120:
                        last_recognition_time = current_time
                        # 短暂暂停，模拟人类注意力分散
                        if random.random() < 0.1:  # 10%的概率
                            time.sleep(random.uniform(0.5, 1.5))
                    
                except KeyboardInterrupt:
                    # 处理用户中断
                    log_message = f"[{time.strftime('%H:%M:%S')}] 监控被用户中断"
                    log_messages.append(log_message)
                    try:
                        update_log_display()
                    except:
                        print(log_message)
                    break
                    
                except Exception as e:
                    error_count += 1
                    log_message = f"[{time.strftime('%H:%M:%S')}] 监控线程外层错误 ({error_count}/{max_errors}): {str(e)}"
                    log_messages.append(log_message)
                    try:
                        update_log_display()
                        status_var.set(f"监控出错: {e}")
                    except:
                        print(log_message)
                    
                    # 清理资源
                    try:
                        screenshot_cache.clear()
                        template_cache.clear()
                        import gc
                        gc.collect()
                        # 强制清理内存工作集
                        if sys.platform == 'win32':
                            try:
                                current_process = ctypes.windll.kernel32.GetCurrentProcess()
                                ctypes.windll.psapi.EmptyWorkingSet(current_process)
                            except:
                                pass
                    except:
                        pass
                    
                    # 如果连续错误过多，停止监控
                    if error_count >= max_errors:
                        log_message = f"[{time.strftime('%H:%M:%S')}] 监控任务连续错误过多，停止监控"
                        log_messages.append(log_message)
                        try:
                            update_log_display()
                            status_var.set("监控因连续错误而停止")
                        except:
                            print(log_message)
                        break
                    
                    # 添加短暂延迟，避免连续错误导致CPU占用过高
                    time.sleep(min(5.0 * error_count, 15.0))  # 递增延迟，最多15秒
        
        except Exception as e:
            log_message = f"[{time.strftime('%H:%M:%S')}] 监控线程致命错误: {str(e)}"
            log_messages.append(log_message)
            try:
                update_log_display()
                status_var.set("监控线程崩溃")
            except:
                print(log_message)
        
        finally:
            # 确保监控状态被正确设置
            monitoring_active = False
            try:
                monitor_button.config(text="开始监控")
                status_var.set("监控已停止")
            except:
                pass
            
            # 最终清理所有资源
            try:
                screenshot_cache.clear()
                template_cache.clear()
                import gc
                gc.collect()
                
                # 强制清理内存
                if sys.platform == 'win32':
                    try:
                        current_process = ctypes.windll.kernel32.GetCurrentProcess()
                        ctypes.windll.psapi.EmptyWorkingSet(current_process)
                    except:
                        pass
                        
                log_message = f"[{time.strftime('%H:%M:%S')}] 监控线程已安全退出，资源已清理"
                log_messages.append(log_message)
                try:
                    update_log_display()
                except:
                    print(log_message)
            except:
                pass
    
    # 选择进程
    def select_process(rect_index):
        global rect_pid_mapping
        
        # 获取WoW进程列表
        wow_processes = get_wow_processes()
        
        if not wow_processes:
            messagebox.showinfo("提示", "未找到运行中的魔兽世界进程！")
            return
        
        # 创建进程选择对话框
        process_window = tk.Toplevel(control_panel)
        process_window.title("选择目标应用进程")
        process_window.geometry("400x450")
        process_window.transient(control_panel)  # 设置为控制面板的子窗口
        process_window.grab_set()  # 模态窗口
        process_window.configure(bg="#f0f0f0")
        
        # 创建列表框
        ttk.Label(process_window, text=f"为{rect_custom_names[rect_index]}选择目标应用进程:", style="Header.TLabel").pack(pady=10)
        
        # 添加滚动条
        list_frame = ttk.Frame(process_window)
        list_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        process_listbox = tk.Listbox(list_frame, height=15, width=40, font=("Microsoft YaHei", 10), yscrollcommand=scrollbar.set)
        process_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=process_listbox.yview)
        
        # 添加进程到列表，显示更多信息
        for pid in wow_processes:
            try:
                proc = psutil.Process(pid)
                # 获取进程创建时间
                create_time = time.strftime("%H:%M:%S", time.localtime(proc.create_time()))
                # 获取内存使用
                memory_mb = proc.memory_info().rss / (1024 * 1024)
                process_listbox.insert(tk.END, f"PID: {pid} | 启动时间: {create_time} | 内存: {memory_mb:.1f} MB")
            except:
                process_listbox.insert(tk.END, f"PID: {pid}")
        
        # 确认按钮
        def confirm_selection():
            selection = process_listbox.curselection()
            if selection:
                index = selection[0]
                pid = wow_processes[index]
                
                # 为特定区域设置进程ID
                rect_pid_mapping[rect_index] = pid
                # 更新矩形标签颜色，表示已关联进程
                canvas.itemconfig(text_labels[rect_index], fill="#00AA00")
                # 更新区域PID显示
                rect_pid_vars[rect_index].set(f"{rect_custom_names[rect_index]} PID: {pid}")
                
                # 立即保存PID映射到配置文件
                try:
                    config_manager.update_rect_pid_mapping(rect_pid_mapping)
                    log_message = f"[{time.strftime('%H:%M:%S')}] 已保存 {rect_custom_names[rect_index]} 关联进程 PID: {pid} 到配置文件"
                    log_messages.append(log_message)
                    update_log_display()
                except Exception as e:
                    log_message = f"[{time.strftime('%H:%M:%S')}] 保存PID映射失败: {str(e)}"
                    log_messages.append(log_message)
                    update_log_display()
                
                messagebox.showinfo("成功", f"已将{rect_custom_names[rect_index]}与进程 PID: {pid} 关联并保存到配置文件")
                
                process_window.destroy()
            else:
                messagebox.showwarning("警告", "请先选择一个进程！")
        
        button_frame = ttk.Frame(process_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="确认选择", command=confirm_selection).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=process_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    # 创建主控制选项卡的内容
    control_frame = ttk.Frame(control_tab)
    control_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 创建左右分栏布局
    left_frame = ttk.Frame(control_frame)
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    right_frame = ttk.Frame(control_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 创建信息框架 (左侧)
    info_frame = ttk.LabelFrame(left_frame, text="状态信息")
    info_frame.pack(fill=tk.X, padx=5, pady=5)
    
    # 状态信息标签
    ttk.Label(info_frame, textvariable=status_var).pack(anchor=tk.W, padx=5, pady=2)
    ttk.Label(info_frame, textvariable=folder_var).pack(anchor=tk.W, padx=5, pady=2)
    ttk.Label(info_frame, textvariable=rect_var).pack(anchor=tk.W, padx=5, pady=2)
    
    # 创建操作框架 (左侧)
    action_frame = ttk.LabelFrame(left_frame, text="操作")
    action_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 添加按钮，使用更现代的样式和布局
    button_frame = ttk.Frame(action_frame)
    button_frame.pack(fill=tk.X, padx=10, pady=10)
    
    # 选择图像文件夹按钮
    folder_button = ttk.Button(button_frame, text="选择图像文件夹", command=select_image_folder)
    folder_button.pack(fill=tk.X, pady=5)
    
    # 显示/隐藏边框按钮
    frame_button = ttk.Button(button_frame, text="显示/隐藏边框", command=toggle_frames)
    frame_button.pack(fill=tk.X, pady=5)
    
    # 开始/停止监控按钮 - 使用强调色
    monitor_button = ttk.Button(button_frame, text="开始监控", command=toggle_monitoring, style="Accent.TButton")
    monitor_button.pack(fill=tk.X, pady=5)
    
    # 手动触发按钮
    manual_button = ttk.Button(button_frame, text="手动触发识别", command=recognize_image)
    manual_button.pack(fill=tk.X, pady=5)

    # 保存配置按钮
    def save_all_config():
        try:
            # 保存PID映射
            config_manager.update_rect_pid_mapping(rect_pid_mapping)

            # 保存阈值设置
            config_manager.update_threshold_settings(sub_threshold_min, sub_threshold_max, threshold_mode)
            config_manager.update_recognition_threshold(recognition_threshold)

            # 添加日志记录
            log_message = f"[{time.strftime('%H:%M:%S')}] 所有配置已保存到配置文件"
            log_messages.append(log_message)
            update_log_display()

            messagebox.showinfo("成功", "所有配置已保存到配置文件！\n下次启动时将自动加载这些设置。")
        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            log_message = f"[{time.strftime('%H:%M:%S')}] {error_msg}"
            log_messages.append(log_message)
            update_log_display()
            messagebox.showerror("错误", error_msg)

    save_config_button = ttk.Button(button_frame, text="保存所有配置", command=save_all_config)
    save_config_button.pack(fill=tk.X, pady=5)

    # 监控状态检查按钮
    def check_monitoring_status():
        try:
            import psutil
            current_process = psutil.Process()
            memory_percent = current_process.memory_percent()
            cpu_percent = current_process.cpu_percent()

            status_info = f"监控状态检查:\n"
            status_info += f"• 监控活跃: {'是' if monitoring_active else '否'}\n"
            status_info += f"• 监控线程: {'运行中' if monitoring_thread and monitoring_thread.is_alive() else '已停止'}\n"
            status_info += f"• 内存使用: {memory_percent:.1f}%\n"
            status_info += f"• CPU使用: {cpu_percent:.1f}%\n"
            status_info += f"• 模板图像数量: {len(template_images)}\n"
            status_info += f"• 关联进程数量: {sum(1 for pid in rect_pid_mapping.values() if pid is not None)}\n"

            # 检查进程是否仍然存在
            active_processes = 0
            for i, pid in rect_pid_mapping.items():
                if pid is not None:
                    try:
                        psutil.Process(pid)
                        active_processes += 1
                    except psutil.NoSuchProcess:
                        status_info += f"• 警告: {rect_custom_names[i]} 关联的进程 PID {pid} 已不存在\n"

            status_info += f"• 活跃进程数量: {active_processes}\n"

            messagebox.showinfo("监控状态", status_info)

            # 记录状态检查
            log_message = f"[{time.strftime('%H:%M:%S')}] 监控状态检查完成 - 内存: {memory_percent:.1f}%, CPU: {cpu_percent:.1f}%"
            log_messages.append(log_message)
            update_log_display()

        except Exception as e:
            messagebox.showerror("错误", f"状态检查失败: {str(e)}")

    status_check_button = ttk.Button(button_frame, text="检查监控状态", command=check_monitoring_status)
    status_check_button.pack(fill=tk.X, pady=5)
    
    # 区域进程关联框架
    rect_frame = ttk.LabelFrame(left_frame, text="区域进程关联")
    rect_frame.pack(fill=tk.X, padx=5, pady=5)
    
    # 为每个区域创建关联按钮
    for i in range(4):
        rect_button_frame = ttk.Frame(rect_frame)
        rect_button_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # 显示区域PID的标签
        ttk.Label(rect_button_frame, textvariable=rect_pid_vars[i], width=20).pack(side=tk.LEFT, padx=5)
        
        # 关联进程的按钮
        ttk.Button(rect_button_frame, text="关联进程", 
                 command=lambda idx=i: select_process(idx)).pack(side=tk.RIGHT, padx=5)
    
    # 配置区域按钮网格列权重
    rect_button_frame.columnconfigure(0, weight=1)
    rect_button_frame.columnconfigure(1, weight=1)
    rect_button_frame.columnconfigure(2, weight=1)
    rect_button_frame.columnconfigure(3, weight=1)
    
    # 配置网格列权重
    button_frame.columnconfigure(0, weight=1)
    button_frame.columnconfigure(1, weight=1)
    button_frame.columnconfigure(2, weight=1)
    button_frame.columnconfigure(3, weight=1)
    
    # 创建模板图像列表框架 (左侧)
    templates_frame = ttk.LabelFrame(left_frame, text="已加载的模板图像")
    templates_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 创建模板图像列表框
    templates_listbox = tk.Listbox(templates_frame, height=5)
    templates_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 创建日志框架 (右侧)
    log_frame = ttk.LabelFrame(right_frame, text="操作日志")
    log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 创建日志文本框和滚动条，支持水平和垂直滚动
    # 创建容器框架，用于正确布局文本框和滚动条
    log_container = ttk.Frame(log_frame)
    log_container.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
    
    # 垂直滚动条
    log_v_scroll = ttk.Scrollbar(log_container, orient="vertical")
    log_v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 水平滚动条
    log_h_scroll = ttk.Scrollbar(log_container, orient="horizontal")
    log_h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
    
    # 文本框
    log_textbox = tk.Text(log_container, height=20, width=50, 
                         yscrollcommand=log_v_scroll.set,
                         xscrollcommand=log_h_scroll.set,
                         wrap=tk.NONE,  # 禁用自动换行，使水平滚动有效
                         font=("Microsoft YaHei", 9), bg="#FAFEFF", fg=text_color)
    log_textbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    # 连接滚动条到文本框
    log_v_scroll.config(command=log_textbox.yview)
    log_h_scroll.config(command=log_textbox.xview)
    log_textbox.config(state=tk.DISABLED)  # 设置为只读
    
    # 创建设置选项卡的内容
    settings_frame = ttk.Frame(settings_tab)
    settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 创建左右分栏布局，调整比例
    settings_left_frame = ttk.Frame(settings_frame)
    settings_left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5, anchor=tk.N)
    
    # 添加左侧滚动框架，支持水平和垂直滚动
    # 创建容器框架以正确布局滚动条
    left_scroll_container = ttk.Frame(settings_left_frame)
    left_scroll_container.pack(fill=tk.BOTH, expand=True)
    
    left_canvas = tk.Canvas(left_scroll_container, bg=bg_color)
    
    # 添加垂直滚动条
    left_v_scrollbar = ttk.Scrollbar(left_scroll_container, orient="vertical", command=left_canvas.yview)
    left_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 添加水平滚动条
    left_h_scrollbar = ttk.Scrollbar(left_scroll_container, orient="horizontal", command=left_canvas.xview)
    left_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    
    left_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    left_scrollable_frame = ttk.Frame(left_canvas)
    
    left_scrollable_frame.bind(
        "<Configure>",
        lambda e: left_canvas.configure(
            scrollregion=left_canvas.bbox("all")
        )
    )
    
    left_canvas.create_window((0, 0), window=left_scrollable_frame, anchor="nw")
    left_canvas.configure(yscrollcommand=left_v_scrollbar.set, xscrollcommand=left_h_scrollbar.set)
    
    left_canvas.pack(side="left", fill="both", expand=True)
    left_v_scrollbar.pack(side="right", fill="y")
    left_h_scrollbar.pack(side="bottom", fill="x")
    
    settings_right_frame = ttk.Frame(settings_frame)
    settings_right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5, anchor=tk.N)
    
    # 设置选项
    ttk.Label(left_scrollable_frame, text="基本设置", style="Header.TLabel").pack(anchor=tk.W, padx=5, pady=5)
    
    # 创建阈值调整框架
    threshold_frame = ttk.LabelFrame(left_scrollable_frame, text="图像识别阈值设置")
    threshold_frame.pack(fill=tk.X, padx=5, pady=10)
    
    # 阈值显示标签
    threshold_var = tk.StringVar()
    threshold_var.set(f"当前阈值: {recognition_threshold:.2f}")
    ttk.Label(threshold_frame, textvariable=threshold_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=5)
    
    # 阈值调整滑动条
    def update_threshold(val):
        global recognition_threshold
        # 将字符串转换为浮点数
        recognition_threshold = float(val)
        threshold_var.set(f"当前阈值: {recognition_threshold:.2f}")
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 图像识别阈值已调整为: {recognition_threshold:.2f}"
        log_messages.append(log_message)
        update_log_display()
        # 保存配置
        config_manager.update_config('recognition_threshold', recognition_threshold)
    
    threshold_scale = ttk.Scale(threshold_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, length=300, value=recognition_threshold, command=update_threshold)
    threshold_scale.pack(fill=tk.X, padx=10, pady=10)
    
    # 添加阈值模式选择
    ttk.Separator(threshold_frame).pack(fill=tk.X, padx=5, pady=10)
    ttk.Label(threshold_frame, text="阈值模式选择", font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=5)
    
    # 阈值模式选择框架
    threshold_mode_frame = ttk.Frame(threshold_frame)
    threshold_mode_frame.pack(fill=tk.X, padx=5, pady=5)
    
    threshold_mode_var = tk.StringVar(value=threshold_mode)
    
    # 固定阈值模式
    fixed_radio = ttk.Radiobutton(threshold_mode_frame, text="固定阈值模式", 
                                 variable=threshold_mode_var, value="fixed")
    fixed_radio.pack(anchor=tk.W, padx=5, pady=2)
    
    # 范围阈值模式  
    range_radio = ttk.Radiobutton(threshold_mode_frame, text="范围阈值模式", 
                                 variable=threshold_mode_var, value="range")
    range_radio.pack(anchor=tk.W, padx=5, pady=2)
    
    def update_threshold_mode():
        global threshold_mode
        threshold_mode = threshold_mode_var.get()
        # 添加日志记录
        mode_text = "固定阈值模式" if threshold_mode == "fixed" else "范围阈值模式"
        log_message = f"[{time.strftime('%H:%M:%S')}] 阈值模式已切换为: {mode_text}"
        log_messages.append(log_message)
        update_log_display()
        # 保存配置
        config_manager.update_config('threshold_mode', threshold_mode)
    
    # 绑定模式切换事件
    fixed_radio.config(command=update_threshold_mode)
    range_radio.config(command=update_threshold_mode)
    
    # 添加说明文本
    ttk.Label(threshold_frame, text="提示: 阈值越高要求匹配度越精确，阈值越低容易误判。推荐值: 0.6-0.8", font=("Microsoft YaHei", 8)).pack(anchor=tk.W, padx=5, pady=5)
    ttk.Label(threshold_frame, text="固定阈值模式: 大于或等于设定阈值才算识别成功", font=("Microsoft YaHei", 8), foreground="#2E86AB").pack(anchor=tk.W, padx=5, pady=2)
    ttk.Label(threshold_frame, text="范围阈值模式: 只有在设定范围内的匹配度才算识别成功", font=("Microsoft YaHei", 8), foreground="#A23B72").pack(anchor=tk.W, padx=5, pady=2)
    
    # 创建子框调整框架
    subrect_frame = ttk.LabelFrame(left_scrollable_frame, text="识别范围子框调整")
    subrect_frame.pack(fill=tk.X, padx=5, pady=10)
    
    # 创建子框选择下拉菜单
    subrect_select_frame = ttk.Frame(subrect_frame)
    subrect_select_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Label(subrect_select_frame, text="选择要调整的子框:", font=("Microsoft YaHei", 10)).pack(side=tk.LEFT, padx=5, pady=5)
    
    selected_subrect = tk.IntVar()
    selected_subrect.set(0)  # 默认选择第一个子框
    
    subrect_combo = ttk.Combobox(subrect_select_frame, textvariable=selected_subrect, width=10, font=("Microsoft YaHei", 10))
    subrect_combo['values'] = [0, 1, 2, 3]  # 四个子框的索引
    subrect_combo['state'] = 'readonly'  # 设置为只读
    subrect_combo.pack(side=tk.LEFT, padx=5, pady=5)
    
    # 创建子框参数调整滑动条
    def update_subrect_display():
        # 更新当前选择的子框参数显示
        idx = selected_subrect.get()
        x_var.set(f"X位置: {sub_rect_coords[idx][0]:.2f}")
        y_var.set(f"Y位置: {sub_rect_coords[idx][1]:.2f}")
        width_var.set(f"宽度: {sub_rect_coords[idx][2]:.2f}")
        height_var.set(f"高度: {sub_rect_coords[idx][3]:.2f}")
        
        # 更新滑动条的值
        x_scale.set(sub_rect_coords[idx][0])
        y_scale.set(sub_rect_coords[idx][1])
        width_scale.set(sub_rect_coords[idx][2])
        height_scale.set(sub_rect_coords[idx][3])
        
        # 更新自定义名称和颜色
        sub_name_var.set(sub_rect_custom_names[idx])
        sub_color_var.set(sub_rect_custom_colors[idx])
    
    # 当选择不同的子框时更新显示
    def on_subrect_selected(event):
        update_subrect_display()
    
    subrect_combo.bind('<<ComboboxSelected>>', on_subrect_selected)
    
    # 更新子框位置和大小的函数
    def update_subrect():
        idx = selected_subrect.get()
        rect_coord = rect_coords[idx]
        x1, y1, x2, y2 = rect_coord
        sub_x_percent, sub_y_percent, sub_width_percent, sub_height_percent = sub_rect_coords[idx]
        
        # 计算子框的实际坐标
        sub_x1 = x1 + (x2 - x1) * sub_x_percent
        sub_y1 = y1 + (y2 - y1) * sub_y_percent
        sub_x2 = sub_x1 + (x2 - x1) * sub_width_percent
        sub_y2 = sub_y1 + (y2 - y1) * sub_height_percent
        
        # 更新子框位置和大小
        canvas.coords(sub_rectangles[idx], sub_x1, sub_y1, sub_x2, sub_y2)
        
        # 更新子框标签位置
        canvas.coords(sub_text_labels[idx], sub_x1 + 20, sub_y1 + 20)
        
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 子框 0.{idx+1} 位置和大小已更新"
        log_messages.append(log_message)
        update_log_display()
    
    # 创建滑动条容器框架
    sliders_frame = ttk.Frame(subrect_frame)
    sliders_frame.pack(fill=tk.X, padx=5, pady=5)
    
    # X位置调整
    x_var = tk.StringVar()
    x_var.set(f"X位置: {sub_rect_coords[0][0]:.2f}")
    ttk.Label(sliders_frame, textvariable=x_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_x(val):
        idx = selected_subrect.get()
        sub_rect_coords[idx][0] = float(val)
        x_var.set(f"X位置: {sub_rect_coords[idx][0]:.2f}")
        update_subrect()
        # 保存子框坐标配置
        config_manager.update_sub_rect_coord(idx, sub_rect_coords[idx])
    
    x_scale = ttk.Scale(sliders_frame, from_=0.0, to=0.5, orient=tk.HORIZONTAL, length=300, value=sub_rect_coords[0][0], command=update_x)
    x_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # Y位置调整
    y_var = tk.StringVar()
    y_var.set(f"Y位置: {sub_rect_coords[0][1]:.2f}")
    ttk.Label(sliders_frame, textvariable=y_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_y(val):
        idx = selected_subrect.get()
        sub_rect_coords[idx][1] = float(val)
        y_var.set(f"Y位置: {sub_rect_coords[idx][1]:.2f}")
        update_subrect()
        # 保存子框坐标配置
        config_manager.update_sub_rect_coord(idx, sub_rect_coords[idx])
    
    y_scale = ttk.Scale(sliders_frame, from_=0.0, to=0.5, orient=tk.HORIZONTAL, length=300, value=sub_rect_coords[0][1], command=update_y)
    y_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 宽度调整
    width_var = tk.StringVar()
    width_var.set(f"宽度: {sub_rect_coords[0][2]:.2f}")
    ttk.Label(sliders_frame, textvariable=width_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_width(val):
        idx = selected_subrect.get()
        sub_rect_coords[idx][2] = float(val)
        width_var.set(f"宽度: {sub_rect_coords[idx][2]:.2f}")
        update_subrect()
        # 保存子框坐标配置
        config_manager.update_sub_rect_coord(idx, sub_rect_coords[idx])
    
    width_scale = ttk.Scale(sliders_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, length=300, value=sub_rect_coords[0][2], command=update_width)
    width_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 高度调整
    height_var = tk.StringVar()
    height_var.set(f"高度: {sub_rect_coords[0][3]:.2f}")
    ttk.Label(sliders_frame, textvariable=height_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_height(val):
        idx = selected_subrect.get()
        sub_rect_coords[idx][3] = float(val)
        height_var.set(f"高度: {sub_rect_coords[idx][3]:.2f}")
        update_subrect()
        # 保存子框坐标配置
        config_manager.update_sub_rect_coord(idx, sub_rect_coords[idx])
    
    height_scale = ttk.Scale(sliders_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, length=300, value=sub_rect_coords[0][3], command=update_height)
    height_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 添加子框识别阈值范围设置
    ttk.Separator(sliders_frame).pack(fill=tk.X, padx=5, pady=10)
    ttk.Label(sliders_frame, text="子框识别阈值范围设置", font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=5)
    
    # 子框阈值最小值
    sub_threshold_min_var = tk.StringVar()
    sub_threshold_min_var.set(f"最小阈值: 0.61")
    ttk.Label(sliders_frame, textvariable=sub_threshold_min_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_sub_threshold_min(val):
        global sub_threshold_min
        sub_threshold_min = float(val)
        sub_threshold_min_var.set(f"最小阈值: {sub_threshold_min:.2f}")
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 子框识别最小阈值已调整为: {sub_threshold_min:.2f}"
        log_messages.append(log_message)
        update_log_display()
        # 保存配置
        config_manager.update_config('sub_threshold_min', sub_threshold_min)
    
    sub_threshold_min_scale = ttk.Scale(sliders_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, length=300, value=0.61, command=update_sub_threshold_min)
    sub_threshold_min_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 子框阈值最大值
    sub_threshold_max_var = tk.StringVar()
    sub_threshold_max_var.set(f"最大阈值: 0.81")
    ttk.Label(sliders_frame, textvariable=sub_threshold_max_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_sub_threshold_max(val):
        global sub_threshold_max
        sub_threshold_max = float(val)
        sub_threshold_max_var.set(f"最大阈值: {sub_threshold_max:.2f}")
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 子框识别最大阈值已调整为: {sub_threshold_max:.2f}"
        log_messages.append(log_message)
        update_log_display()
        # 保存配置
        config_manager.update_config('sub_threshold_max', sub_threshold_max)
    
    sub_threshold_max_scale = ttk.Scale(sliders_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, length=300, value=0.81, command=update_sub_threshold_max)
    sub_threshold_max_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 添加说明文本
    ttk.Label(sliders_frame, text="提示: 调整子框的位置和大小，子框内是实际进行图像识别的区域。X和Y值越小，子框越靠近左上角；宽度和高度值越大，子框越大。", font=("Microsoft YaHei", 8)).pack(anchor=tk.W, padx=5, pady=5)
    ttk.Label(sliders_frame, text="重要: 子框图像识别阈值范围可调整，只有在设定范围内的匹配度才会被识别为成功。", font=("Microsoft YaHei", 8), foreground="#E74C3C").pack(anchor=tk.W, padx=5, pady=2)
    
    # 添加自定义名称和颜色设置
    ttk.Separator(sliders_frame).pack(fill=tk.X, padx=5, pady=10)
    
    # 自定义名称设置
    ttk.Label(sliders_frame, text="自定义名称:", font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    sub_name_var = tk.StringVar()
    sub_name_var.set(sub_rect_custom_names[0])
    sub_name_entry = ttk.Entry(sliders_frame, textvariable=sub_name_var, width=30)
    sub_name_entry.pack(fill=tk.X, padx=5, pady=2)
    
    def update_sub_name():
        idx = selected_subrect.get()
        new_name = sub_name_var.get()
        if new_name:
            sub_rect_custom_names[idx] = new_name
            # 更新画布上的标签
            if frames_visible and sub_text_labels[idx]:
                canvas.itemconfig(sub_text_labels[idx], text=new_name)
            
            # 添加日志记录
            log_message = f"[{time.strftime('%H:%M:%S')}] 子框 {idx+1} 名称已更新为: {new_name}"
            log_messages.append(log_message)
            update_log_display()
            
            # 保存配置
            config_manager.update_config('sub_rect_custom_names', {str(i): sub_rect_custom_names[i] for i in range(4)})
    
    ttk.Button(sliders_frame, text="更新名称", command=update_sub_name).pack(anchor=tk.E, padx=5, pady=2)
    
    # 自定义颜色设置
    ttk.Label(sliders_frame, text="自定义颜色:", font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    sub_color_frame = ttk.Frame(sliders_frame)
    sub_color_frame.pack(fill=tk.X, padx=5, pady=2)
    
    sub_color_var = tk.StringVar()
    sub_color_var.set(sub_rect_custom_colors[0])
    sub_color_entry = ttk.Entry(sub_color_frame, textvariable=sub_color_var, width=20)
    sub_color_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
    
    # 添加颜色预览
    ttk.Label(sub_color_frame, text="预览:").pack(side=tk.LEFT, padx=5, pady=2)
    sub_color_preview = tk.Label(sub_color_frame, width=3, bg=sub_rect_custom_colors[0])
    sub_color_preview.pack(side=tk.LEFT, padx=5, pady=2)
    
    def choose_sub_color():
        idx = selected_subrect.get()
        color = colorchooser.askcolor(initialcolor=sub_rect_custom_colors[idx], title=f"选择{sub_rect_custom_names[idx]}的颜色")
        if color[1]:
            sub_color_var.set(color[1])
            sub_color_preview.config(bg=color[1])
    
    ttk.Button(sub_color_frame, text="选择颜色", command=choose_sub_color).pack(side=tk.LEFT, padx=5, pady=2)
    
    def update_sub_color():
        idx = selected_subrect.get()
        new_color = sub_color_var.get()
        if new_color:
            try:
                # 验证颜色格式
                if not new_color.startswith('#') or len(new_color) != 7:
                    raise ValueError("颜色格式不正确")
                
                # 尝试解析颜色
                int(new_color[1:], 16)
                
                # 更新颜色预览
                sub_color_preview.config(bg=new_color)
                
                sub_rect_custom_colors[idx] = new_color
                # 更新画布上的框框和标签颜色
                if frames_visible and sub_rectangles[idx]:
                    canvas.itemconfig(sub_rectangles[idx], outline=new_color)
                    canvas.itemconfig(sub_text_labels[idx], fill=new_color)
                
                # 添加日志记录
                log_message = f"[{time.strftime('%H:%M:%S')}] {sub_rect_custom_names[idx]} 颜色已更新为: {new_color}"
                log_messages.append(log_message)
                update_log_display()
                
                # 保存配置
                config_manager.update_config('sub_rect_custom_colors', {str(i): sub_rect_custom_colors[i] for i in range(4)})
            except ValueError:
                messagebox.showerror("错误", "颜色格式不正确，请使用HEX格式 (例如: #16A085)")
    
    ttk.Button(sub_color_frame, text="更新颜色", command=update_sub_color).pack(side=tk.LEFT, padx=5, pady=2)
    
    # 当选择不同的子框时更新显示
    def on_subrect_selected_extended(event):
        update_subrect_display()
    
    # 重新绑定选择事件
    subrect_combo.unbind('<<ComboboxSelected>>')
    subrect_combo.bind('<<ComboboxSelected>>', on_subrect_selected_extended)
    
    # 创建冷却时间调整框架
    cooldown_frame = ttk.LabelFrame(left_scrollable_frame, text="冷却时间设置")
    cooldown_frame.pack(fill=tk.X, padx=5, pady=10)
    
    # 冷却时间显示标签
    cooldown_var = tk.StringVar()
    cooldown_var.set(f"当前冷却时间: {cooldown_time} 秒")
    ttk.Label(cooldown_frame, textvariable=cooldown_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=5)
    
    # 冷却时间调整滑动条
    def update_cooldown(val):
        global cooldown_time
        # 将字符串转换为整数
        cooldown_time = int(float(val))
        cooldown_var.set(f"当前冷却时间: {cooldown_time} 秒")
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 冷却时间已调整为: {cooldown_time} 秒"
        log_messages.append(log_message)
        update_log_display()
        # 保存配置
        config_manager.update_config('cooldown_time', cooldown_time)
    
    cooldown_scale = ttk.Scale(cooldown_frame, from_=1, to=60, orient=tk.HORIZONTAL, length=300, value=cooldown_time, command=update_cooldown)
    cooldown_scale.pack(fill=tk.X, padx=5, pady=5)
    
    # 添加随机冷却时间范围设置
    cooldown_random_var = tk.StringVar()
    cooldown_random_var.set(f"冷却时间随机范围: ±{cooldown_random_range} 秒")
    ttk.Label(cooldown_frame, textvariable=cooldown_random_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=5)
    
    # 随机冷却时间范围调整滑动条
    def update_cooldown_random(val):
        global cooldown_random_range
        # 将字符串转换为整数
        cooldown_random_range = int(float(val))
        cooldown_random_var.set(f"冷却时间随机范围: ±{cooldown_random_range} 秒")
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 冷却时间随机范围已调整为: ±{cooldown_random_range} 秒"
        log_messages.append(log_message)
        update_log_display()
        # 保存配置
        config_manager.update_config('cooldown_random_range', cooldown_random_range)
    
    cooldown_random_scale = ttk.Scale(cooldown_frame, from_=0, to=20, orient=tk.HORIZONTAL, length=300, value=cooldown_random_range, command=update_cooldown_random)
    cooldown_random_scale.pack(fill=tk.X, padx=5, pady=5)
    
    # 添加说明文本
    ttk.Label(cooldown_frame, text="提示: 冷却时间是指每个区域触发S键后需要等待的时间，每个区域有独立的冷却计时。范围: 1-60秒", font=("Microsoft YaHei", 8)).pack(anchor=tk.W, padx=5, pady=5)
    ttk.Label(cooldown_frame, text="提示: 冷却时间随机范围是在基础冷却时间上增加的随机值范围，设为0表示不随机。", font=("Microsoft YaHei", 8)).pack(anchor=tk.W, padx=5, pady=5)
    
    # 创建新子框设置框架
    ttk.Label(settings_right_frame, text="新子框设置", style="Header.TLabel").pack(anchor=tk.W, padx=5, pady=5)
    
    # 创建带滚动条的新子框设置框架
    new_subrect_outer_frame = ttk.Frame(settings_right_frame)
    new_subrect_outer_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 添加滚动条，支持水平和垂直滚动
    # 创建容器框架
    new_subrect_container = ttk.Frame(new_subrect_outer_frame)
    new_subrect_container.pack(fill=tk.BOTH, expand=True)
    
    # 垂直滚动条
    new_subrect_v_scrollbar = ttk.Scrollbar(new_subrect_container, orient="vertical")
    new_subrect_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 水平滚动条
    new_subrect_h_scrollbar = ttk.Scrollbar(new_subrect_container, orient="horizontal")
    new_subrect_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    
    # Canvas
    new_subrect_canvas = tk.Canvas(new_subrect_container, 
                                 bg=bg_color,
                                 yscrollcommand=new_subrect_v_scrollbar.set,
                                 xscrollcommand=new_subrect_h_scrollbar.set)
    new_subrect_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    # 设置滚动命令
    new_subrect_v_scrollbar.config(command=new_subrect_canvas.yview)
    new_subrect_h_scrollbar.config(command=new_subrect_canvas.xview)
    
    # 创建内容框架
    new_subrect_scrollable_frame = ttk.Frame(new_subrect_canvas)
    
    new_subrect_scrollable_frame.bind(
        "<Configure>",
        lambda e: new_subrect_canvas.configure(
            scrollregion=new_subrect_canvas.bbox("all")
        )
    )
    
    new_subrect_canvas.create_window((0, 0), window=new_subrect_scrollable_frame, anchor="nw")
    
    # 创建新子框设置框架
    new_subrect_frame = ttk.LabelFrame(new_subrect_scrollable_frame, text="新识别范围子框设置")
    new_subrect_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)
    
    # 创建新子框选择下拉菜单
    new_subrect_select_frame = ttk.Frame(new_subrect_frame)
    new_subrect_select_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Label(new_subrect_select_frame, text="选择要调整的新子框:", font=("Microsoft YaHei", 10)).pack(side=tk.LEFT, padx=5, pady=5)
    
    selected_new_subrect = tk.IntVar()
    selected_new_subrect.set(0)  # 默认选择第一个新子框
    
    new_subrect_combo = ttk.Combobox(new_subrect_select_frame, textvariable=selected_new_subrect, width=10, font=("Microsoft YaHei", 10))
    new_subrect_combo['values'] = [0, 1, 2, 3]  # 四个新子框的索引
    new_subrect_combo['state'] = 'readonly'  # 设置为只读
    new_subrect_combo.pack(side=tk.LEFT, padx=5, pady=5)
    
    # 创建新子框参数调整滑动条
    def update_new_subrect_display():
        # 更新当前选择的新子框参数显示
        idx = selected_new_subrect.get()
        new_x_var.set(f"X位置: {new_sub_rect_coords[idx][0]:.2f}")
        new_y_var.set(f"Y位置: {new_sub_rect_coords[idx][1]:.2f}")
        new_width_var.set(f"宽度: {new_sub_rect_coords[idx][2]:.2f}")
        new_height_var.set(f"高度: {new_sub_rect_coords[idx][3]:.2f}")
        
        # 更新滑动条的值
        new_x_scale.set(new_sub_rect_coords[idx][0])
        new_y_scale.set(new_sub_rect_coords[idx][1])
        new_width_scale.set(new_sub_rect_coords[idx][2])
        new_height_scale.set(new_sub_rect_coords[idx][3])
        
        # 更新阈值显示
        new_threshold_var.set(f"识别阈值: {new_sub_recognition_thresholds[idx]:.2f}")
        new_threshold_scale.set(new_sub_recognition_thresholds[idx])
        
        # 更新冷却时间显示
        new_cooldown_var.set(f"冷却时间: {new_sub_cooldown_times[idx]} 秒")
        new_cooldown_scale.set(new_sub_cooldown_times[idx])
    
    # 当选择不同的新子框时更新显示
    def on_new_subrect_selected(event):
        update_new_subrect_display()
    
    new_subrect_combo.bind('<<ComboboxSelected>>', on_new_subrect_selected)
    
    # 创建新子框图像文件夹选择按钮
    new_image_folder_frame = ttk.Frame(new_subrect_frame)
    new_image_folder_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Label(new_image_folder_frame, text="新子框图像文件夹:", font=("Microsoft YaHei", 10, "bold")).pack(side=tk.LEFT, padx=5, pady=5)
    
    new_image_folder_var = tk.StringVar()
    new_image_folder_var.set("未选择")
    
    new_image_folder_label = ttk.Label(new_image_folder_frame, textvariable=new_image_folder_var, font=("Microsoft YaHei", 9))
    new_image_folder_label.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
    
    def select_new_image_folder():
        idx = selected_new_subrect.get()
        folder = filedialog.askdirectory(title=f"为新子框 N.{idx+1} 选择图像文件夹")
        if folder:
            new_sub_image_folders[idx] = folder
            new_image_folder_var.set(folder)
            
            # 加载新子框的模板图像
            try:
                # 清空当前模板
                new_sub_template_images[idx] = []
                
                # 加载文件夹中的图像作为模板
                image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
                if image_files:
                    for img_file in image_files[:50]:  # 最多加载50张图片
                        img_path = os.path.join(folder, img_file)
                        template = cv2.imread(img_path)
                        if template is not None:
                            new_sub_template_images[idx].append((img_file, template))
                    
                    # 添加日志记录
                    log_message = f"[{time.strftime('%H:%M:%S')}] 新子框 N.{idx+1} 已加载 {len(new_sub_template_images[idx])} 张模板图像"
                    log_messages.append(log_message)
                    update_log_display()
                else:
                    messagebox.showwarning("警告", f"所选文件夹中没有支持的图像文件！\n支持的格式: PNG, JPG, JPEG, BMP")
            except Exception as e:
                messagebox.showerror("错误", f"加载图像时出错: {e}")
            
            # 保存配置
            config_manager.update_new_sub_rect_image_folder(idx, folder)
    
    new_folder_button = ttk.Button(new_image_folder_frame, text="选择文件夹", command=select_new_image_folder)
    new_folder_button.pack(side=tk.RIGHT, padx=5, pady=5)
    
    # 创建新子框滑动条容器框架
    new_sliders_frame = ttk.Frame(new_subrect_frame)
    new_sliders_frame.pack(fill=tk.X, padx=5, pady=5)
    
    # 新增：更新新子框位置和大小的函数
    def update_new_subrect():
        idx = selected_new_subrect.get()
        rect_coord = rect_coords[idx]
        x1, y1, x2, y2 = rect_coord
        new_sub_x_percent, new_sub_y_percent, new_sub_width_percent, new_sub_height_percent = new_sub_rect_coords[idx]
        
        # 计算新子框的实际坐标
        new_sub_x1 = x1 + (x2 - x1) * new_sub_x_percent
        new_sub_y1 = y1 + (y2 - y1) * new_sub_y_percent
        new_sub_x2 = new_sub_x1 + (x2 - x1) * new_sub_width_percent
        new_sub_y2 = new_sub_y1 + (y2 - y1) * new_sub_height_percent
        
        # 更新新子框位置和大小
        canvas.coords(new_sub_rectangles[idx], new_sub_x1, new_sub_y1, new_sub_x2, new_sub_y2)
        
        # 更新新子框标签位置
        canvas.coords(new_sub_text_labels[idx], new_sub_x1 + 20, new_sub_y1 + 20)
        
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 新子框 N.{idx+1} 位置和大小已更新"
        log_messages.append(log_message)
        update_log_display()
    
    # X位置调整
    new_x_var = tk.StringVar()
    new_x_var.set(f"X位置: {new_sub_rect_coords[0][0]:.2f}")
    ttk.Label(new_sliders_frame, textvariable=new_x_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_new_x(val):
        idx = selected_new_subrect.get()
        new_sub_rect_coords[idx][0] = float(val)
        new_x_var.set(f"X位置: {new_sub_rect_coords[idx][0]:.2f}")
        update_new_subrect()
        # 保存新子框坐标配置
        config_manager.update_new_sub_rect_coord(idx, new_sub_rect_coords[idx])
    
    new_x_scale = ttk.Scale(new_sliders_frame, from_=0.0, to=0.9, orient=tk.HORIZONTAL, length=300, value=new_sub_rect_coords[0][0], command=update_new_x)
    new_x_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # Y位置调整
    new_y_var = tk.StringVar()
    new_y_var.set(f"Y位置: {new_sub_rect_coords[0][1]:.2f}")
    ttk.Label(new_sliders_frame, textvariable=new_y_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_new_y(val):
        idx = selected_new_subrect.get()
        new_sub_rect_coords[idx][1] = float(val)
        new_y_var.set(f"Y位置: {new_sub_rect_coords[idx][1]:.2f}")
        update_new_subrect()
        # 保存新子框坐标配置
        config_manager.update_new_sub_rect_coord(idx, new_sub_rect_coords[idx])
    
    new_y_scale = ttk.Scale(new_sliders_frame, from_=0.0, to=0.9, orient=tk.HORIZONTAL, length=300, value=new_sub_rect_coords[0][1], command=update_new_y)
    new_y_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 宽度调整
    new_width_var = tk.StringVar()
    new_width_var.set(f"宽度: {new_sub_rect_coords[0][2]:.2f}")
    ttk.Label(new_sliders_frame, textvariable=new_width_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_new_width(val):
        idx = selected_new_subrect.get()
        new_sub_rect_coords[idx][2] = float(val)
        new_width_var.set(f"宽度: {new_sub_rect_coords[idx][2]:.2f}")
        update_new_subrect()
        # 保存新子框坐标配置
        config_manager.update_new_sub_rect_coord(idx, new_sub_rect_coords[idx])
    
    new_width_scale = ttk.Scale(new_sliders_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, length=300, value=new_sub_rect_coords[0][2], command=update_new_width)
    new_width_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 高度调整
    new_height_var = tk.StringVar()
    new_height_var.set(f"高度: {new_sub_rect_coords[0][3]:.2f}")
    ttk.Label(new_sliders_frame, textvariable=new_height_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_new_height(val):
        idx = selected_new_subrect.get()
        new_sub_rect_coords[idx][3] = float(val)
        new_height_var.set(f"高度: {new_sub_rect_coords[idx][3]:.2f}")
        update_new_subrect()
        # 保存新子框坐标配置
        config_manager.update_new_sub_rect_coord(idx, new_sub_rect_coords[idx])
    
    new_height_scale = ttk.Scale(new_sliders_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, length=300, value=new_sub_rect_coords[0][3], command=update_new_height)
    new_height_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 新子框识别阈值调整
    new_threshold_var = tk.StringVar()
    new_threshold_var.set(f"识别阈值: {new_sub_recognition_thresholds[0]:.2f}")
    ttk.Label(new_sliders_frame, textvariable=new_threshold_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_new_threshold(val):
        idx = selected_new_subrect.get()
        new_sub_recognition_thresholds[idx] = float(val)
        new_threshold_var.set(f"识别阈值: {new_sub_recognition_thresholds[idx]:.2f}")
        
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] {new_sub_rect_custom_names[idx]} 识别阈值已调整为: {new_sub_recognition_thresholds[idx]:.2f}"
        log_messages.append(log_message)
        update_log_display()
        
        # 保存配置
        config_manager.update_config('new_sub_rect_thresholds', {str(i): new_sub_recognition_thresholds[i] for i in range(4)})
    
    new_threshold_scale = ttk.Scale(new_sliders_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, length=300, value=new_sub_recognition_thresholds[0], command=update_new_threshold)
    new_threshold_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 新子框禁止操作时间调整
    new_cooldown_var = tk.StringVar()
    new_cooldown_var.set(f"重置冷却时间: {new_sub_cooldown_times[0]} 秒")
    ttk.Label(new_sliders_frame, textvariable=new_cooldown_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_new_cooldown(val):
        idx = selected_new_subrect.get()
        new_sub_cooldown_times[idx] = int(float(val))
        # 同步更新block_times，保持一致性
        new_sub_block_times[idx] = new_sub_cooldown_times[idx]
        new_cooldown_var.set(f"重置冷却时间: {new_sub_cooldown_times[idx]} 秒")
        
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] {new_sub_rect_custom_names[idx]} 重置冷却时间已调整为: {new_sub_cooldown_times[idx]} 秒"
        log_messages.append(log_message)
        update_log_display()
        
        # 保存配置 - 使用与加载时相同的键名
        config_manager.update_new_sub_rect_cooldown_time(idx, new_sub_cooldown_times[idx])
    
    new_cooldown_scale = ttk.Scale(new_sliders_frame, from_=1, to=60, orient=tk.HORIZONTAL, length=300, value=new_sub_block_times[0], command=update_new_cooldown)
    new_cooldown_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 添加新子框随机冷却时间范围设置
    new_cooldown_random_var = tk.StringVar()
    new_cooldown_random_var.set(f"重置冷却时间随机范围: ±{new_sub_cooldown_random_range} 秒")
    ttk.Label(new_sliders_frame, textvariable=new_cooldown_random_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_new_cooldown_random(val):
        global new_sub_cooldown_random_range
        # 将字符串转换为整数
        new_sub_cooldown_random_range = int(float(val))
        new_cooldown_random_var.set(f"重置冷却时间随机范围: ±{new_sub_cooldown_random_range} 秒")
        
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 新子框重置冷却时间随机范围已调整为: ±{new_sub_cooldown_random_range} 秒"
        log_messages.append(log_message)
        update_log_display()
        
        # 保存配置
        config_manager.update_config('new_sub_cooldown_random_range', new_sub_cooldown_random_range)
    
    new_cooldown_random_scale = ttk.Scale(new_sliders_frame, from_=0, to=20, orient=tk.HORIZONTAL, length=300, value=new_sub_cooldown_random_range, command=update_new_cooldown_random)
    new_cooldown_random_scale.pack(fill=tk.X, padx=5, pady=2)
    
    # 添加说明文本
    ttk.Label(new_sliders_frame, text="提示: 新子框用于识别特定图像，当识别到图像时会重置对应主区域的冷却时间。重置冷却时间是指识别到图像后，将主区域的冷却时间重置为指定的秒数。", font=("Microsoft YaHei", 8)).pack(anchor=tk.W, padx=5, pady=5)
    ttk.Label(new_sliders_frame, text="提示: 重置冷却时间随机范围是在基础冷却时间上增加的随机值范围，设为0表示不随机。", font=("Microsoft YaHei", 8)).pack(anchor=tk.W, padx=5, pady=5)
    
    # 添加自定义名称和颜色设置
    ttk.Separator(new_sliders_frame).pack(fill=tk.X, padx=5, pady=10)
    
    # 自定义名称设置
    ttk.Label(new_sliders_frame, text="自定义名称:", font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    new_name_var = tk.StringVar()
    new_name_var.set(new_sub_rect_custom_names[0])
    new_name_entry = ttk.Entry(new_sliders_frame, textvariable=new_name_var, width=30)
    new_name_entry.pack(fill=tk.X, padx=5, pady=2)
    
    def update_new_name():
        idx = selected_new_subrect.get()
        new_name = new_name_var.get()
        if new_name:
            new_sub_rect_custom_names[idx] = new_name
            # 更新画布上的标签
            if frames_visible and new_sub_text_labels[idx]:
                canvas.itemconfig(new_sub_text_labels[idx], text=new_name)
            
            # 添加日志记录
            log_message = f"[{time.strftime('%H:%M:%S')}] 新子框 {idx+1} 名称已更新为: {new_name}"
            log_messages.append(log_message)
            update_log_display()
            
            # 保存配置
            config_manager.update_config('new_sub_rect_custom_names', {str(i): new_sub_rect_custom_names[i] for i in range(4)})
    
    ttk.Button(new_sliders_frame, text="更新名称", command=update_new_name).pack(anchor=tk.E, padx=5, pady=2)
    
    # 自定义颜色设置
    ttk.Label(new_sliders_frame, text="自定义颜色:", font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    
    new_color_frame = ttk.Frame(new_sliders_frame)
    new_color_frame.pack(fill=tk.X, padx=5, pady=2)
    
    new_color_var = tk.StringVar()
    new_color_var.set(new_sub_rect_custom_colors[0])
    new_color_entry = ttk.Entry(new_color_frame, textvariable=new_color_var, width=20)
    new_color_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
    
    # 添加颜色预览
    ttk.Label(new_color_frame, text="预览:").pack(side=tk.LEFT, padx=5, pady=2)
    new_color_preview = tk.Label(new_color_frame, width=3, bg=new_sub_rect_custom_colors[0])
    new_color_preview.pack(side=tk.LEFT, padx=5, pady=2)
    
    def choose_new_color():
        idx = selected_new_subrect.get()
        color = colorchooser.askcolor(initialcolor=new_sub_rect_custom_colors[idx], title=f"选择{new_sub_rect_custom_names[idx]}的颜色")
        if color[1]:
            new_color_var.set(color[1])
            new_color_preview.config(bg=color[1])
    
    ttk.Button(new_color_frame, text="选择颜色", command=choose_new_color).pack(side=tk.LEFT, padx=5, pady=2)
    
    def update_new_color():
        idx = selected_new_subrect.get()
        new_color = new_color_var.get()
        if new_color:
            try:
                # 验证颜色格式
                if not new_color.startswith('#') or len(new_color) != 7:
                    raise ValueError("颜色格式不正确")
                
                # 尝试解析颜色
                int(new_color[1:], 16)
                
                # 更新颜色预览
                new_color_preview.config(bg=new_color)
                
                new_sub_rect_custom_colors[idx] = new_color
                # 更新画布上的框框和标签颜色
                if frames_visible and new_sub_rectangles[idx]:
                    canvas.itemconfig(new_sub_rectangles[idx], outline=new_color)
                    canvas.itemconfig(new_sub_text_labels[idx], fill=new_color)
                
                # 添加日志记录
                log_message = f"[{time.strftime('%H:%M:%S')}] {new_sub_rect_custom_names[idx]} 颜色已更新为: {new_color}"
                log_messages.append(log_message)
                update_log_display()
                
                # 保存配置
                config_manager.update_config('new_sub_rect_custom_colors', {str(i): new_sub_rect_custom_colors[i] for i in range(4)})
            except ValueError:
                messagebox.showerror("错误", "颜色格式不正确，请使用HEX格式 (例如: #ff00ff)")
    
    ttk.Button(new_color_frame, text="更新颜色", command=update_new_color).pack(side=tk.LEFT, padx=5, pady=2)
    
    # 当选择不同的新子框时更新名称和颜色显示
    def on_new_subrect_selected_extended(event):
        update_new_subrect_display()
        idx = selected_new_subrect.get()
        new_name_var.set(new_sub_rect_custom_names[idx])
        new_color_var.set(new_sub_rect_custom_colors[idx])
        new_color_preview.config(bg=new_sub_rect_custom_colors[idx])
        # 更新图像文件夹显示
        if new_sub_image_folders[idx]:
            new_image_folder_var.set(new_sub_image_folders[idx])
        else:
            new_image_folder_var.set("未选择")
    
    # 重新绑定选择事件
    new_subrect_combo.unbind('<<ComboboxSelected>>')
    new_subrect_combo.bind('<<ComboboxSelected>>', on_new_subrect_selected_extended)
    
    # 创建帮助选项卡的内容
    help_frame = ttk.Frame(help_tab)
    help_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 帮助信息
    ttk.Label(help_frame, text="使用帮助", style="Header.TLabel").pack(anchor=tk.W, padx=5, pady=5)
    help_text = """使用步骤：
1. 点击"选择图像文件夹"按钮，选择包含模板图像的文件夹
2. 为每个监控区域关联目标应用进程（使用"为区域X选择进程"按钮）
3. 点击"显示/隐藏边框"按钮，显示四个监控区域和子框
4. 在"设置"选项卡中调整子框（0.1、0.2、0.3、0.4）的位置和大小
5. 在"设置"选项卡中配置新子框（N.1、N.2、N.3、N.4）的位置、大小、阈值和禁止操作时间
6. 在"设置"选项卡底部可以自定义主区域（框框1.2.3.4）的名称和颜色
7. 在"设置"选项卡底部可以启用防检测功能，降低被检测的风险
8. 为新子框选择独立的图像文件夹
9. 点击"开始监控"按钮，开始自动监控
10. 当在子框区域中识别到模板图像时，会自动向该区域关联的进程发送S键
11. 当在新子框区域中识别到模板图像时，会在设定的时间内禁止对应主区域发送S键操作

注意事项：
- 只有关联了进程的区域才会进行图像识别
- 图像识别只在子框（0.1、0.2、0.3、0.4）和新子框（N.1、N.2、N.3、N.4）内进行，而不是整个区域
- 可以通过设置选项卡中的滑动条调整每个子框和新子框的位置和大小
- 每个区域有独立的冷却时间，可在设置选项卡中调整
- 每个新子框有独立的禁止操作时间和识别阈值，可在设置选项卡中调整
- 新子框用于识别特定图像，当识别到图像时会暂时禁止对应主区域的S键操作
- 主区域（框框1.2.3.4）的名称和颜色可以在设置选项卡底部进行自定义，支持预设颜色快速应用
- 防检测功能可以降低被检测的风险，包括随机化操作时间、内存清理和窗口隐藏等功能
- 所有区域和子框都可以自定义名称和颜色，使其更易于识别
- 确保模板图像清晰且特征明显
- 监控区域可以覆盖多个应用窗口
- 可以随时停止监控或手动触发识别
- 支持后台操作，不需要应用窗口处于焦点状态
- 所有设置都会自动保存到配置文件中，下次启动程序时会自动加载
- 程序会记住窗口的位置和大小，下次启动时自动恢复"""
    
    # 创建带水平和垂直滚动条的文本框
    help_container = ttk.Frame(help_frame)
    help_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    help_v_scroll = ttk.Scrollbar(help_container, orient="vertical")
    help_v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    help_h_scroll = ttk.Scrollbar(help_container, orient="horizontal")
    help_h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
    
    help_textbox = tk.Text(help_container, wrap=tk.NONE, height=15, width=50, 
                          font=("Microsoft YaHei", 10), bg="#FAFEFF", fg=text_color,
                          yscrollcommand=help_v_scroll.set,
                          xscrollcommand=help_h_scroll.set)
    help_textbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    help_v_scroll.config(command=help_textbox.yview)
    help_h_scroll.config(command=help_textbox.xview)
    
    help_textbox.insert(tk.END, help_text)
    help_textbox.config(state=tk.DISABLED)  # 设置为只读
    
    # 版本信息
    version_label = ttk.Label(help_frame, text="屏幕区域监控工具 版本: 1.2.5", font=("Microsoft YaHei", 9))
    version_label.pack(side=tk.RIGHT, padx=5, pady=5)
    
    # 配置文件路径信息
    config_path_frame = ttk.Frame(help_frame)
    config_path_frame.pack(fill=tk.X, padx=5, pady=10, side=tk.BOTTOM)
    
    config_path = os.path.abspath(config_manager.CONFIG_FILE)
    ttk.Label(config_path_frame, text="配置文件保存位置:", font=("Microsoft YaHei", 9, "bold")).pack(anchor=tk.W, padx=5, pady=2)
    ttk.Label(config_path_frame, text=config_path, font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=5, pady=2)
    ttk.Label(config_path_frame, text="所有设置（包括子框和新子框的位置、大小、阈值等）都会自动保存到此文件，下次启动时自动加载。", 
             font=("Microsoft YaHei", 9), wraplength=600).pack(anchor=tk.W, padx=5, pady=2)
    
    # 添加主区域自定义设置到设置选项卡底部
    main_rect_settings_frame = ttk.LabelFrame(left_scrollable_frame, text="主区域自定义设置")
    main_rect_settings_frame.pack(fill=tk.X, padx=5, pady=10)
    
    # 添加说明
    ttk.Label(main_rect_settings_frame, text="在这里可以自定义主区域（框框1.2.3.4）的名称和颜色", 
             font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=5, pady=5)
    
    # 创建表格布局
    table_frame = ttk.Frame(main_rect_settings_frame)
    table_frame.pack(padx=5, pady=5, fill=tk.X)
    
    # 表头
    ttk.Label(table_frame, text="区域", width=8).grid(row=0, column=0, padx=5, pady=5)
    ttk.Label(table_frame, text="名称", width=12).grid(row=0, column=1, padx=5, pady=5)
    ttk.Label(table_frame, text="颜色", width=12).grid(row=0, column=2, padx=5, pady=5)
    ttk.Label(table_frame, text="预览", width=6).grid(row=0, column=3, padx=5, pady=5)
    ttk.Label(table_frame, text="操作", width=20).grid(row=0, column=4, padx=5, pady=5)
    
    # 为每个区域创建一行
    for i in range(4):
        # 区域编号
        ttk.Label(table_frame, text=f"区域 {i+1}", width=8).grid(row=i+1, column=0, padx=5, pady=5)
        
        # 名称输入
        name_var = tk.StringVar(value=rect_custom_names[i])
        name_entry = ttk.Entry(table_frame, textvariable=name_var, width=12)
        name_entry.grid(row=i+1, column=1, padx=5, pady=5)
        
        # 颜色输入
        color_var = tk.StringVar(value=rect_custom_colors[i])
        color_entry = ttk.Entry(table_frame, textvariable=color_var, width=12)
        color_entry.grid(row=i+1, column=2, padx=5, pady=5)
        
        # 颜色预览
        color_preview = tk.Label(table_frame, width=4, bg=rect_custom_colors[i], relief="solid", borderwidth=1)
        color_preview.grid(row=i+1, column=3, padx=5, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(table_frame)
        button_frame.grid(row=i+1, column=4, padx=5, pady=5)
        
        # 更新名称按钮
        def create_update_name_func(index, entry):
            def update_name():
                new_name = entry.get()
                if new_name:
                    rect_custom_names[index] = new_name
                    # 更新画布上的标签
                    if frames_visible and text_labels[index]:
                        canvas.itemconfig(text_labels[index], text=new_name)
                    
                    # 更新区域PID显示
                    if rect_pid_mapping[index] is not None:
                        rect_pid_vars[index].set(f"{new_name} PID: {rect_pid_mapping[index]}")
                    else:
                        rect_pid_vars[index].set(f"{new_name} PID: 未选择")
                    
                    # 添加日志记录
                    log_message = f"[{time.strftime('%H:%M:%S')}] 主区域 {index+1} 名称已更新为: {new_name}"
                    log_messages.append(log_message)
                    update_log_display()
                    
                    # 保存配置
                    config_manager.update_config('rect_custom_names', {str(j): rect_custom_names[j] for j in range(4)})
            return update_name
        
        update_name_cmd = create_update_name_func(i, name_entry)
        ttk.Button(button_frame, text="更新名称", command=update_name_cmd, width=8).pack(side=tk.LEFT, padx=2)
        
        # 更新颜色按钮
        def create_update_color_func(index, entry, preview):
            def update_color():
                new_color = entry.get()
                if new_color:
                    try:
                        # 验证颜色格式
                        if not new_color.startswith('#') or len(new_color) != 7:
                            raise ValueError("颜色格式不正确")
                        
                        # 尝试解析颜色
                        int(new_color[1:], 16)
                        
                        # 更新颜色预览
                        preview.config(bg=new_color)
                        
                        rect_custom_colors[index] = new_color
                        # 更新画布上的框框和标签颜色
                        if frames_visible and rectangles[index]:
                            if not area_in_cooldown[index]:  # 只有在非冷却状态下才更新颜色
                                canvas.itemconfig(rectangles[index], outline=new_color)
                            canvas.itemconfig(text_labels[index], fill=new_color)
                        
                        # 添加日志记录
                        log_message = f"[{time.strftime('%H:%M:%S')}] 主区域 {index+1} 颜色已更新为: {new_color}"
                        log_messages.append(log_message)
                        update_log_display()
                        
                        # 保存配置
                        config_manager.update_config('rect_custom_colors', {str(j): rect_custom_colors[j] for j in range(4)})
                    except ValueError:
                        messagebox.showerror("错误", "颜色格式不正确，请使用HEX格式 (例如: #5B9BD5)")
            return update_color
        
        update_color_cmd = create_update_color_func(i, color_entry, color_preview)
        ttk.Button(button_frame, text="更新颜色", command=update_color_cmd, width=8).pack(side=tk.LEFT, padx=2)
        
        # 选择颜色按钮
        def create_choose_color_func(index, entry, preview):
            def choose_color():
                color = colorchooser.askcolor(initialcolor=rect_custom_colors[index], title=f"选择区域 {index+1} 的颜色")
                if color[1]:
                    entry.delete(0, tk.END)
                    entry.insert(0, color[1])
                    preview.config(bg=color[1])
            return choose_color
        
        choose_color_cmd = create_choose_color_func(i, color_entry, color_preview)
        ttk.Button(button_frame, text="选择颜色", command=choose_color_cmd, width=8).pack(side=tk.LEFT, padx=2)
    
    # 添加预设颜色部分
    preset_frame = ttk.Frame(main_rect_settings_frame)
    preset_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Label(preset_frame, text="预设颜色快速应用:", font=("Microsoft YaHei", 9, "bold")).pack(anchor=tk.W, padx=5, pady=5)
    
    # 选择区域和颜色的框架
    select_color_frame = ttk.Frame(preset_frame)
    select_color_frame.pack(fill=tk.X, padx=5, pady=5)
    
    # 选择区域
    ttk.Label(select_color_frame, text="选择区域:").pack(side=tk.LEFT, padx=5, pady=5)
    
    selected_area = tk.IntVar(value=1)
    area_combo = ttk.Combobox(select_color_frame, textvariable=selected_area, width=5, state="readonly")
    area_combo['values'] = [1, 2, 3, 4]
    area_combo.pack(side=tk.LEFT, padx=5, pady=5)
    
    # 预设颜色按钮
    preset_colors = [
        ("#5B9BD5", "蓝色"), 
        ("#ED7D31", "橙色"), 
        ("#A5A5A5", "灰色"), 
        ("#FFC000", "黄色"),
        ("#4472C4", "深蓝色"), 
        ("#70AD47", "绿色"), 
        ("#FF0000", "红色"), 
        ("#7030A0", "紫色")
    ]
    
    # 创建颜色按钮框架
    color_buttons_frame = ttk.Frame(preset_frame)
    color_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
    
    for i, (color, name) in enumerate(preset_colors):
        # 创建颜色按钮
        color_button = tk.Label(color_buttons_frame, width=3, height=1, bg=color, relief="raised", borderwidth=2)
        color_button.grid(row=0, column=i, padx=5, pady=5)
        
        ttk.Label(color_buttons_frame, text=name, font=("Microsoft YaHei", 8)).grid(row=1, column=i, padx=5, pady=2)
        
        # 点击颜色按钮的事件
        def apply_preset_color(event, color_code=color, color_name=name):
            idx = selected_area.get() - 1
            
            # 更新颜色
            rect_custom_colors[idx] = color_code
            
            # 更新画布上的框框和标签颜色
            if frames_visible and rectangles[idx]:
                if not area_in_cooldown[idx]:
                    canvas.itemconfig(rectangles[idx], outline=color_code)
                canvas.itemconfig(text_labels[idx], fill=color_code)
            
            # 更新表格中的颜色预览和输入框
            for widget in table_frame.winfo_children():
                grid_info = widget.grid_info()
                if grid_info and 'row' in grid_info and grid_info['row'] == idx + 1:
                    if isinstance(widget, ttk.Entry) and grid_info['column'] == 2:
                        widget.delete(0, tk.END)
                        widget.insert(0, color_code)
                    elif isinstance(widget, tk.Label) and grid_info['column'] == 3:
                        widget.config(bg=color_code)
            
            # 添加日志记录
            log_message = f"[{time.strftime('%H:%M:%S')}] 主区域 {idx+1} 颜色已更新为: {color_code} ({color_name})"
            log_messages.append(log_message)
            update_log_display()
            
            # 保存配置
            config_manager.update_config('rect_custom_colors', {str(j): rect_custom_colors[j] for j in range(4)})
        
        color_button.bind("<Button-1>", lambda event, c=color, n=name: apply_preset_color(event, c, n))
    
    # 添加说明文本
    ttk.Label(main_rect_settings_frame, text="提示: 颜色使用HEX格式，例如 #5B9BD5。可以直接输入颜色代码，也可以使用颜色选择器或预设颜色。", 
             font=("Microsoft YaHei", 8)).pack(anchor=tk.W, padx=5, pady=5)
    
    # 添加防检测设置框架
    anti_detection_frame = ttk.LabelFrame(left_scrollable_frame, text="防检测设置")
    anti_detection_frame.pack(fill=tk.X, padx=5, pady=10)
    
    # 添加说明
    ttk.Label(anti_detection_frame, text="启用防检测功能可以降低被检测的风险，但可能会影响性能", 
             font=("Microsoft YaHei", 9)).pack(anchor=tk.W, padx=5, pady=5)
    
    # 防检测开关
    anti_detection_var = tk.BooleanVar(value=anti_detection_enabled)
    anti_detection_check = ttk.Checkbutton(anti_detection_frame, text="启用防检测功能", 
                                         variable=anti_detection_var)
    anti_detection_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_anti_detection():
        global anti_detection_enabled
        anti_detection_enabled = anti_detection_var.get()
        if anti_detection_enabled:
            enable_anti_detection()
        else:
            disable_anti_detection()
    
    anti_detection_check.config(command=toggle_anti_detection)
    
    # 隐藏窗口标题选项
    hide_title_var = tk.BooleanVar(value=hide_window_title)
    hide_title_check = ttk.Checkbutton(anti_detection_frame, text="隐藏窗口标题", 
                                     variable=hide_title_var)
    hide_title_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_hide_title():
        global hide_window_title
        hide_window_title = hide_title_var.get()
        config_manager.update_config('hide_window_title', hide_window_title)
        
        # 立即应用设置
        if anti_detection_enabled:
            if hide_window_title:
                try:
                    hwnd = win32gui.GetForegroundWindow()
                    win32gui.SetWindowText(hwnd, "")
                except Exception as e:
                    print(f"隐藏窗口标题失败: {e}")
            else:
                try:
                    hwnd = win32gui.GetForegroundWindow()
                    win32gui.SetWindowText(hwnd, "屏幕区域监控工具")
                except Exception as e:
                    print(f"恢复窗口标题失败: {e}")
    
    hide_title_check.config(command=toggle_hide_title)
    
    # 隐藏窗口选项
    hide_window_var = tk.BooleanVar(value=hide_window_from_taskbar)
    hide_window_check = ttk.Checkbutton(anti_detection_frame, text="隐藏窗口（从任务栏移除）", 
                                      variable=hide_window_var)
    hide_window_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_hide_window():
        global hide_window_from_taskbar
        hide_window_from_taskbar = hide_window_var.get()
        config_manager.update_config('hide_window_from_taskbar', hide_window_from_taskbar)
        
        # 立即应用设置
        if anti_detection_enabled:
            if hide_window_from_taskbar:
                try:
                    hwnd = win32gui.GetForegroundWindow()
                    win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, 
                                        win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE) | win32con.WS_EX_TOOLWINDOW)
                    win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                except Exception as e:
                    print(f"隐藏窗口失败: {e}")
            else:
                try:
                    hwnd = win32gui.GetForegroundWindow()
                    win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, 
                                        win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE) & ~win32con.WS_EX_TOOLWINDOW)
                    win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                except Exception as e:
                    print(f"恢复窗口失败: {e}")
    
    hide_window_check.config(command=toggle_hide_window)
    
    # 隐藏进程选项
    hide_process_var = tk.BooleanVar(value=hide_process_enabled)
    hide_process_check = ttk.Checkbutton(anti_detection_frame, text="隐藏进程（降低检测优先级）", 
                                       variable=hide_process_var)
    hide_process_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_hide_process():
        global hide_process_enabled
        hide_process_enabled = hide_process_var.get()
        config_manager.update_config('hide_process_enabled', hide_process_enabled)
        
        # 如果同时启用了防检测功能，则立即应用
        if anti_detection_enabled and hide_process_enabled:
            try:
                # 获取当前进程ID
                current_pid = os.getpid()
                # 修改进程优先级为低，使其不易被检测
                process = psutil.Process(current_pid)
                process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
                
                # 随机设置CPU亲和性，避免使用所有核心
                cpu_count = psutil.cpu_count()
                if cpu_count > 1:
                    # 随机选择一半或更少的CPU核心
                    core_count = random.randint(1, max(1, cpu_count // 2))
                    cores = random.sample(range(cpu_count), core_count)
                    mask = sum(1 << i for i in cores)
                    process.cpu_affinity([i for i in cores])
                
                # 添加日志记录
                log_message = f"[{time.strftime('%H:%M:%S')}] 进程隐藏功能已启用"
                log_messages.append(log_message)
                update_log_display()
            except Exception as e:
                print(f"隐藏进程失败: {e}")
    
    hide_process_check.config(command=toggle_hide_process)
    
    # 随机化时间选项
    randomize_timing_var = tk.BooleanVar(value=randomize_timing)
    randomize_timing_check = ttk.Checkbutton(anti_detection_frame, text="随机化操作时间（增加不规则性）", 
                                           variable=randomize_timing_var)
    randomize_timing_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_randomize_timing():
        global randomize_timing
        randomize_timing = randomize_timing_var.get()
        config_manager.update_config('randomize_timing', randomize_timing)
    
    randomize_timing_check.config(command=toggle_randomize_timing)
    
    # 更详细的防检测说明文本
    ttk.Label(anti_detection_frame, text="提示: 防检测功能包括进程伪装为lghub.exe、隐藏窗口和降低进程优先级。这些功能可以降低被检测的风险，但可能会影响程序性能。", 
             font=("Microsoft YaHei", 8), wraplength=580).pack(anchor=tk.W, padx=5, pady=5)
    
    # 添加高级防检测选项
    ttk.Separator(anti_detection_frame).pack(fill=tk.X, padx=5, pady=10)
    ttk.Label(anti_detection_frame, text="高级防检测选项", font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=5)
    
    # 图像识别方法选择
    img_method_frame = ttk.Frame(anti_detection_frame)
    img_method_frame.pack(fill=tk.X, padx=5, pady=5)
    
    ttk.Label(img_method_frame, text="图像识别方法:").pack(side=tk.LEFT, padx=5, pady=5)
    
    img_method_var = tk.StringVar(value=img_recognize_method)
    img_method_combo = ttk.Combobox(img_method_frame, textvariable=img_method_var, width=10)
    img_method_combo['values'] = ['normal', 'safe', 'stealth']
    img_method_combo['state'] = 'readonly'
    img_method_combo.pack(side=tk.LEFT, padx=5, pady=5)
    
    def update_img_method(event=None):
        global img_recognize_method
        img_recognize_method = img_method_var.get()
        config_manager.update_config('img_recognize_method', img_recognize_method)
        
        # 根据选择更新说明文本
        if img_recognize_method == 'normal':
            img_method_desc.set("普通模式: 标准图像识别，速度快但容易被检测")
        elif img_recognize_method == 'safe':
            img_method_desc.set("安全模式: 分块识别并随机延迟，平衡安全性和性能")
        elif img_recognize_method == 'stealth':
            img_method_desc.set("隐身模式: 添加噪声并使用多种策略，最高安全性但性能较低")
        
        # 添加日志记录
        log_message = f"[{time.strftime('%H:%M:%S')}] 图像识别方法已设置为: {img_recognize_method}"
        log_messages.append(log_message)
        update_log_display()
    
    img_method_combo.bind('<<ComboboxSelected>>', update_img_method)
    
    # 图像识别方法说明
    img_method_desc = tk.StringVar()
    # 设置初始说明
    if img_recognize_method == 'normal':
        img_method_desc.set("普通模式: 标准图像识别，速度快但容易被检测")
    elif img_recognize_method == 'safe':
        img_method_desc.set("安全模式: 分块识别并随机延迟，平衡安全性和性能")
    elif img_recognize_method == 'stealth':
        img_method_desc.set("隐身模式: 添加噪声并使用多种策略，最高安全性但性能较低")
    
    ttk.Label(anti_detection_frame, textvariable=img_method_desc, 
             font=("Microsoft YaHei", 8), wraplength=580).pack(anchor=tk.W, padx=5, pady=5)
    
    # 随机化截图方式
    random_screenshot_var = tk.BooleanVar(value=randomize_screenshot)
    random_screenshot_check = ttk.Checkbutton(anti_detection_frame, text="随机化截图方法（降低API调用特征）", 
                                            variable=random_screenshot_var)
    random_screenshot_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_random_screenshot():
        global randomize_screenshot
        randomize_screenshot = random_screenshot_var.get()
        config_manager.update_config('randomize_screenshot', randomize_screenshot)
    
    random_screenshot_check.config(command=toggle_random_screenshot)
    
    # 防止内存扫描
    anti_memory_scan_var = tk.BooleanVar(value=anti_memory_scan)
    anti_memory_scan_check = ttk.Checkbutton(anti_detection_frame, text="防止内存扫描（保护进程内存不被读取）", 
                                           variable=anti_memory_scan_var)
    anti_memory_scan_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_anti_memory_scan():
        global anti_memory_scan
        anti_memory_scan = anti_memory_scan_var.get()
        config_manager.update_config('anti_memory_scan', anti_memory_scan)
    
    anti_memory_scan_check.config(command=toggle_anti_memory_scan)
    
    # 掩盖内存特征
    mask_memory_var = tk.BooleanVar(value=mask_memory_signature)
    mask_memory_check = ttk.Checkbutton(anti_detection_frame, text="掩盖内存特征（混淆内存中的特征识别）", 
                                      variable=mask_memory_var)
    mask_memory_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_mask_memory():
        global mask_memory_signature
        mask_memory_signature = mask_memory_var.get()
        config_manager.update_config('mask_memory_signature', mask_memory_signature)
    
    mask_memory_check.config(command=toggle_mask_memory)
    
    # 增强进程名保护
    secure_process_name_var = tk.BooleanVar(value=secure_process_name)
    secure_process_name_check = ttk.Checkbutton(anti_detection_frame, text="增强进程名保护（随机伪装为系统进程）", 
                                              variable=secure_process_name_var)
    secure_process_name_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def toggle_secure_process_name():
        global secure_process_name
        secure_process_name = secure_process_name_var.get()
        config_manager.update_config('secure_process_name', secure_process_name)
    
    secure_process_name_check.config(command=toggle_secure_process_name)
    
    # 更新高级选项的启用状态
    def update_advanced_options_state():
        state = "normal" if anti_detection_enabled else "disabled"
        img_method_combo.config(state="readonly" if anti_detection_enabled else "disabled")
        random_screenshot_check.config(state=state)
        anti_memory_scan_check.config(state=state)
        mask_memory_check.config(state=state)
        secure_process_name_check.config(state=state)
    
    # 初始设置高级选项状态
    update_advanced_options_state()
    
    # 更新防检测功能的说明文本
    ttk.Label(anti_detection_frame, text="高级防检测说明: 图像识别增强保护可以有效规避检测系统。隐身模式为最高安全性但可能影响性能。随机化截图可以避免特征被检测。", 
             font=("Microsoft YaHei", 8), wraplength=580).pack(anchor=tk.W, padx=5, pady=5)
    
    # 在设置界面中添加主区域大小调整功能
    # 创建主区域大小调整框架
    rect_size_frame = ttk.LabelFrame(left_scrollable_frame, text="主监控区域大小调整")
    rect_size_frame.pack(fill=tk.X, padx=5, pady=10)
    
    # 当前尺寸显示标签
    rect_size_var = tk.StringVar()
    rect_size_var.set(f"当前尺寸: {rect_width} × {rect_height} 像素")
    ttk.Label(rect_size_frame, textvariable=rect_size_var, font=("Microsoft YaHei", 10, "bold")).pack(anchor=tk.W, padx=5, pady=5)
    
    # 宽度调整
    ttk.Label(rect_size_frame, text="宽度:", font=("Microsoft YaHei", 10)).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_rect_width(val):
        global rect_width
        rect_width = int(float(val))
        rect_size_var.set(f"当前尺寸: {rect_width} × {rect_height} 像素")
        # 注意：这里不调用update_rect_dimensions()，而是在应用按钮点击时调用
    
    width_scale = ttk.Scale(rect_size_frame, from_=200, to=1200, orient=tk.HORIZONTAL, length=300, value=rect_width, command=update_rect_width)
    width_scale.pack(fill=tk.X, padx=10, pady=2)
    
    # 高度调整
    ttk.Label(rect_size_frame, text="高度:", font=("Microsoft YaHei", 10)).pack(anchor=tk.W, padx=5, pady=2)
    
    def update_rect_height(val):
        global rect_height
        rect_height = int(float(val))
        rect_size_var.set(f"当前尺寸: {rect_width} × {rect_height} 像素")
        # 注意：这里不调用update_rect_dimensions()，而是在应用按钮点击时调用
    
    height_scale = ttk.Scale(rect_size_frame, from_=200, to=1200, orient=tk.HORIZONTAL, length=300, value=rect_height, command=update_rect_height)
    height_scale.pack(fill=tk.X, padx=10, pady=2)
    
    # 应用按钮
    def apply_rect_dimensions():
        update_rect_dimensions()
        
        # 如果边框当前可见，更新显示
        if frames_visible:
            overlay.deiconify()  # 确保窗口可见
    
    apply_button = ttk.Button(rect_size_frame, text="应用尺寸调整", command=apply_rect_dimensions, style="Accent.TButton")
    apply_button.pack(anchor=tk.E, padx=10, pady=10)
    
    # 添加说明文本
    ttk.Label(rect_size_frame, text="提示: 调整尺寸会影响所有四个监控区域。确保尺寸适合您的屏幕分辨率。点击'应用尺寸调整'后生效。", 
             font=("Microsoft YaHei", 8)).pack(anchor=tk.W, padx=5, pady=5)
    
    # 启动应用程序
    return control_panel

# 增强防检测功能
def enhanced_anti_detection():
    """增强版防检测功能，包括图像识别保护、内存特征掩盖和进程伪装"""
    global anti_detection_enabled, process_name_disguise
    
    if not anti_detection_enabled:
        return
    
    # 记录开始时间以监控执行时间
    start_time = time.time()
    
    try:
        # 1. 随机选择进程伪装名称
        if secure_process_name and process_disguise_list:
            process_name_disguise = random.choice(process_disguise_list)
            # 尝试更改当前进程名称显示
            try:
                current_pid = os.getpid()
                hwnd = win32gui.GetForegroundWindow()
                win32gui.SetWindowText(hwnd, process_name_disguise.split('.')[0])
            except Exception as e:
                if debug_mode:
                    print(f"进程名称伪装失败 (不影响功能): {e}")
        
        # 2. 内存保护 - 混淆内存特征
        if anti_memory_scan:
            try:
                # 强制垃圾回收
                import gc
                gc.collect()
                
                # 通过分配大量小内存块来混淆内存特征
                if mask_memory_signature:
                    # 创建随机大小的内存对象
                    dummy_objects = []
                    for _ in range(random.randint(5, 20)):
                        # 创建随机大小的字符串对象(1KB-50KB)
                        size = random.randint(1024, 51200)
                        # 使用随机字符填充
                        chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
                        random_string = ''.join(random.choice(chars) for _ in range(size))
                        dummy_objects.append(random_string)
                    
                    # 延迟一段随机时间后释放这些对象
                    time.sleep(random.uniform(0.01, 0.05))
                    dummy_objects = None
                    gc.collect()
                
                # 减少工作集
                if sys.platform == 'win32':
                    current_process = ctypes.windll.kernel32.GetCurrentProcess()
                    ctypes.windll.psapi.EmptyWorkingSet(current_process)
            except Exception as e:
                if debug_mode:
                    print(f"内存保护失败 (不影响功能): {e}")
    except Exception as e:
        if debug_mode:
            print(f"增强防检测功能执行失败 (不影响主功能): {e}")
    
    # 确保函数执行时间不规则 (0.01-0.1秒)
    elapsed = time.time() - start_time
    if elapsed < 0.1 and randomize_timing:
        time.sleep(random.uniform(0.01, 0.1 - elapsed))

# 安全截图函数 - 防止被检测
def secure_screenshot(region=None):
    """使用更安全的方式截图，减少被检测风险"""
    if not anti_detection_enabled or not randomize_screenshot:
        # 如果未启用防检测，使用正常的截图方法
        return pyautogui.screenshot(region=region)
    
    try:
        # 随机选择截图方法
        screenshot_method = random.randint(0, 2)
        
        if screenshot_method == 0:
            # 方法1: 使用PyAutoGUI (基础方法)
            screenshot = pyautogui.screenshot(region=region)
        elif screenshot_method == 1:
            # 方法2: 使用PIL直接截图
            from PIL import ImageGrab
            if region:
                screenshot = ImageGrab.grab(bbox=region)
            else:
                screenshot = ImageGrab.grab()
        else:
            # 方法3: 使用win32 API截图 (不易被检测)
            import win32gui
            import win32ui
            import win32con
            import win32api
            
            # 获取屏幕DC
            hwin = win32gui.GetDesktopWindow()
            width = win32api.GetSystemMetrics(win32con.SM_CXVIRTUALSCREEN)
            height = win32api.GetSystemMetrics(win32con.SM_CYVIRTUALSCREEN)
            left = win32api.GetSystemMetrics(win32con.SM_XVIRTUALSCREEN)
            top = win32api.GetSystemMetrics(win32con.SM_YVIRTUALSCREEN)
            
            if region:
                left += region[0]
                top += region[1]
                width = region[2]
                height = region[3]
            
            hwindc = win32gui.GetWindowDC(hwin)
            srcdc = win32ui.CreateDCFromHandle(hwindc)
            memdc = srcdc.CreateCompatibleDC()
            bmp = win32ui.CreateBitmap()
            bmp.CreateCompatibleBitmap(srcdc, width, height)
            memdc.SelectObject(bmp)
            memdc.BitBlt((0, 0), (width, height), srcdc, (left, top), win32con.SRCCOPY)
            
            # 转换为PIL Image
            from PIL import Image
            bmpinfo = bmp.GetInfo()
            bmpstr = bmp.GetBitmapBits(True)
            screenshot = Image.frombuffer(
                'RGB',
                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                bmpstr, 'raw', 'BGRX', 0, 1)
            
            # 清理资源
            memdc.DeleteDC()
            win32gui.ReleaseDC(hwin, hwindc)
        
        # 随机添加轻微的延迟
        if randomize_timing:
            time.sleep(random.uniform(0.001, 0.01))
        
        return screenshot
    
    except Exception as e:
        # 如果安全截图失败，回退到标准方法
        if debug_mode:
            print(f"安全截图失败，使用标准方法 (不影响功能): {e}")
        return pyautogui.screenshot(region=region)

# 安全的图像识别函数
def secure_image_recognition(image, template, method=None):
    """使用更安全的方式进行图像识别"""
    # 首先检查模板大小是否大于图像
    img_h, img_w = image.shape[:2]
    template_h, template_w = template.shape[:2]
    
    # 如果模板大于图像，则抛出异常
    if template_h > img_h or template_w > img_w:
        raise ValueError(f"模板尺寸({template_w}x{template_h})大于图像尺寸({img_w}x{img_h})，无法进行匹配")
    
    if not anti_detection_enabled or img_recognize_method == 'normal':
        # 使用标准方法
        return cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
    
    try:
        # 根据设置选择识别方法
        if method or img_recognize_method == 'safe':
            # 安全模式: 分块识别并随机延迟
            height, width = image.shape[:2]
            template_h, template_w = template.shape[:2]
            
            # 确定分块大小
            block_size = max(template_h * 2, template_w * 2)
            
            # 创建结果矩阵
            result = np.zeros((height - template_h + 1, width - template_w + 1), dtype=np.float32)
            
            # 分块进行匹配
            for y in range(0, height - template_h + 1, block_size):
                for x in range(0, width - template_w + 1, block_size):
                    # 定义块的大小
                    end_y = min(y + block_size + template_h - 1, height)
                    end_x = min(x + block_size + template_w - 1, width)
                    
                    # 提取图像块
                    img_block = image[y:end_y, x:end_x]
                    
                    # 在这个块上执行模板匹配
                    block_result = cv2.matchTemplate(img_block, template, cv2.TM_CCOEFF_NORMED)
                    
                    # 复制结果到主结果矩阵
                    result_h, result_w = block_result.shape
                    result[y:y+result_h, x:x+result_w] = block_result
                    
                    # 添加微小随机延迟，避免被检测
                    if randomize_timing:
                        time.sleep(random.uniform(0.001, 0.005))
            
            return result
        elif img_recognize_method == 'stealth':
            # 隐身模式: 添加随机噪声并使用多尺度匹配
            # 添加微小的噪声到图像副本
            image_copy = image.copy()
            noise = np.random.normal(0, 2, image_copy.shape).astype(np.uint8)
            image_copy = cv2.add(image_copy, noise)
            
            # 使用标准方法
            result = cv2.matchTemplate(image_copy, template, cv2.TM_CCOEFF_NORMED)
            
            # 随机延迟
            if randomize_timing:
                time.sleep(random.uniform(0.002, 0.008))
                
            return result
    except Exception as e:
        if debug_mode:
            print(f"安全图像识别失败，使用标准方法 (不影响功能): {e}")
    
    # 如果以上方法失败，回退到标准方法
    return cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)

# 添加调试模式开关 - 默认关闭
debug_mode = config.get('debug_mode', False)

# 主程序入口
if __name__ == "__main__":
    # 记录启动日志
    start_time = time.strftime('%H:%M:%S')
    print(f"[{start_time}] 程序启动中...")
    
    try:
        # 加载配置
        config = config_manager.load_config()
        
        # 验证并初始化必要的配置项
        required_configs = [
            'recognition_threshold', 'cooldown_time', 'cooldown_random_range',
            'sub_rect_coords', 'new_sub_rect_coords', 'new_sub_rect_thresholds',
            'new_sub_rect_image_folders', 'new_sub_rect_cooldown_time',
            'new_sub_cooldown_random_range'
        ]
        
        # 检查必要的配置是否存在
        config_valid = True
        for cfg in required_configs:
            if cfg not in config:
                print(f"[{start_time}] 错误: 配置中缺少必要项 '{cfg}'")
                config_valid = False
        
        if not config_valid:
            print(f"[{start_time}] 错误: 配置文件无效，将使用默认配置")
            # 使用默认配置
            config = config_manager.DEFAULT_CONFIG.copy()
        
        log_messages.append(f"[{start_time}] 屏幕区域监控工具 v1.2.5 启动成功")
        
        # 从配置中加载上次使用的图像文件夹
        image_folder = config.get('image_folder')
        
        # 创建并启动主窗口
        app = create_window()
        
        # 在创建窗口后自动加载上次使用的图像文件夹
        if image_folder:
            # 加载文件夹中的所有图像作为模板
            template_images = []
            try:
                image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
                if image_files:
                    # 加载前5个图像作为模板
                    loaded_count = 0
                    skipped_count = 0
                    
                    for img_file in image_files[:5]:
                        try:
                            img_path = os.path.join(image_folder, img_file)
                            template = cv2.imread(img_path)
                            if template is not None:
                                # 检查模板大小
                                h, w = template.shape[:2]
                                if h > rect_height or w > rect_width:
                                    # 如果模板太大，记录并跳过
                                    log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 自动加载的模板 {img_file} ({w}x{h}) 可能过大，已跳过"
                                    log_messages.append(log_message)
                                    skipped_count += 1
                                    continue
                                
                                template_images.append((img_file, template))
                                loaded_count += 1
                        except Exception as e:
                            log_message = f"[{time.strftime('%H:%M:%S')}] 加载图像 {img_file} 时出错: {str(e)}"
                            log_messages.append(log_message)
                    
                    # 更新UI中的文件夹显示
                    folder_var.set(f"已选择: {os.path.basename(image_folder)}")
                    
                    # 更新模板图像列表
                    for img_file, _ in template_images:
                        templates_listbox.insert(tk.END, img_file)
                    
                    log_message = f"[{time.strftime('%H:%M:%S')}] 已自动加载上次图像文件夹: {image_folder}，加载 {loaded_count} 个模板"
                    if skipped_count > 0:
                        log_message += f"，跳过 {skipped_count} 个不适合的模板"
                    log_messages.append(log_message)
            except Exception as e:
                log_message = f"[{time.strftime('%H:%M:%S')}] 自动加载图像文件夹失败: {str(e)}"
                log_messages.append(log_message)
        
        # 自动加载新子框的图像
        for i in range(4):
            folder = new_sub_image_folders[i]
            if folder and os.path.exists(folder):
                try:
                    # 清空当前模板
                    new_sub_template_images[i] = []
                    
                    # 计算这个新子框的最大可能尺寸(相对于主矩形)
                    new_sub_x_percent, new_sub_y_percent, new_sub_width_percent, new_sub_height_percent = new_sub_rect_coords[i]
                    max_width = int(rect_width * new_sub_width_percent)
                    max_height = int(rect_height * new_sub_height_percent)
                    
                    # 加载文件夹中的图像作为模板
                    image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
                    if image_files:
                        loaded_count = 0
                        skipped_count = 0
                        
                        for img_file in image_files[:50]:  # 最多加载50张图片
                            try:
                                img_path = os.path.join(folder, img_file)
                                template = cv2.imread(img_path)
                                if template is not None:
                                    # 检查模板大小
                                    h, w = template.shape[:2]
                                    if h > max_height or w > max_width:
                                        # 如果模板太大，记录并跳过
                                        log_message = f"[{time.strftime('%H:%M:%S')}] 警告: 子框 {i+1} 自动加载的模板 {img_file} ({w}x{h}) 可能过大(最大允许{max_width}x{max_height})，已跳过"
                                        log_messages.append(log_message)
                                        skipped_count += 1
                                        continue
                                    
                                    new_sub_template_images[i].append((img_file, template))
                                    loaded_count += 1
                            except Exception as e:
                                log_message = f"[{time.strftime('%H:%M:%S')}] 加载子框 {i+1} 图像 {img_file} 时出错: {str(e)}"
                                log_messages.append(log_message)
                        
                        # 添加日志记录
                        log_message = f"[{time.strftime('%H:%M:%S')}] 已自动加载新子框 {i+1} 图像文件夹: {folder}，加载 {loaded_count} 个模板"
                        if skipped_count > 0:
                            log_message += f"，跳过 {skipped_count} 个不适合的模板"
                        log_messages.append(log_message)
                except Exception as e:
                    log_message = f"[{time.strftime('%H:%M:%S')}] 自动加载新子框图像文件夹失败: {str(e)}"
                    log_messages.append(log_message)
        
        app.mainloop()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
