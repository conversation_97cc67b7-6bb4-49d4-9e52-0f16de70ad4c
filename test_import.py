#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback

def test_imports():
    """测试所有导入"""
    try:
        print("测试基本导入...")
        import tkinter as tk
        print("✓ tkinter")
        
        import win32gui
        print("✓ win32gui")
        
        import win32con
        print("✓ win32con")
        
        import win32api
        print("✓ win32api")
        
        import os
        print("✓ os")
        
        import psutil
        print("✓ psutil")
        
        import cv2
        print("✓ cv2")
        
        import numpy as np
        print("✓ numpy")
        
        import pyautogui
        print("✓ pyautogui")
        
        import threading
        print("✓ threading")
        
        import time
        print("✓ time")
        
        import random
        print("✓ random")
        
        import ctypes
        print("✓ ctypes")
        
        import subprocess
        print("✓ subprocess")
        
        from tkinter import filedialog, ttk, messagebox, colorchooser
        print("✓ tkinter子模块")
        
        from PIL import Image, ImageTk
        print("✓ PIL")
        
        import win32process
        print("✓ win32process")
        
        import win32security
        print("✓ win32security")
        
        import win32service
        print("✓ win32service")
        
        import win32ts
        print("✓ win32ts")
        
        import win32event
        print("✓ win32event")
        
        import win32job
        print("✓ win32job")
        
        import pywintypes
        print("✓ pywintypes")
        
        import winreg
        print("✓ winreg")
        
        import config_manager
        print("✓ config_manager")
        
        print("\n所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"\n✗ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_syntax():
    """测试语法"""
    try:
        print("\n测试语法...")
        import ast
        with open('lghub.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        ast.parse(content)
        print("✓ 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"✗ 语法错误:")
        print(f"  行号: {e.lineno}")
        print(f"  位置: {e.offset}")
        print(f"  错误: {e.msg}")
        if e.text:
            print(f"  代码: {e.text.strip()}")
        return False
    except Exception as e:
        print(f"✗ 语法检查失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试...")
    
    # 测试导入
    import_ok = test_imports()
    
    # 测试语法
    syntax_ok = test_syntax()
    
    if import_ok and syntax_ok:
        print("\n✓ 所有测试通过！")
        sys.exit(0)
    else:
        print("\n✗ 测试失败！")
        sys.exit(1)
