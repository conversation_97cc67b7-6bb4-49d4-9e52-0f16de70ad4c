{"recognition_threshold": 0.7, "cooldown_time": 18, "sub_rect_coords": {"0": [0.1, 0.1, 0.8, 0.8], "1": [0.1, 0.1, 0.8, 0.8], "2": [0.1, 0.1, 0.8, 0.8], "3": [0.1, 0.1, 0.8, 0.8]}, "new_sub_rect_coords": {"0": [0.6, 0.6, 0.3, 0.3], "1": [0.6, 0.6, 0.3, 0.3], "2": [0.6, 0.6, 0.3, 0.3], "3": [0.6, 0.6, 0.3, 0.3]}, "new_sub_rect_thresholds": {"0": 0.7, "1": 0.7, "2": 0.7, "3": 0.7}, "new_sub_rect_image_folders": {"0": null, "1": null, "2": null, "3": null}, "new_sub_rect_cooldown_time": {"0": 10, "1": 3, "2": 3, "3": 3}, "image_folder": "E:/4tuxiang", "last_saved": "2025-07-26 16:04:47", "rect_custom_names": {"0": "区域1", "1": "区域2", "2": "区域3", "3": "区域4"}, "sub_rect_custom_names": {"0": "子框0.1", "1": "子框0.2", "2": "子框0.3", "3": "子框0.4"}, "new_sub_rect_custom_names": {"0": "新子框N.1", "1": "新子框N.2", "2": "新子框N.3", "3": "新子框N.4"}, "rect_custom_colors": {"0": "#5B9BD5", "1": "#5B9BD5", "2": "#5B9BD5", "3": "#5B9BD5"}, "sub_rect_custom_colors": {"0": "#16A085", "1": "#16A085", "2": "#16A085", "3": "#16A085"}, "new_sub_rect_custom_colors": {"0": "#ff00ff", "1": "#ff00ff", "2": "#ff00ff", "3": "#ff00ff"}, "new_sub_rect_block_time": {"0": 3, "1": 3, "2": 3, "3": 3}, "window_position_x": 427, "window_position_y": 11, "window_width": 1307, "window_height": 999, "rect_width": 479, "rect_height": 1001, "hide_window_from_taskbar": true, "memory_cleaning_interval": 300, "randomize_timing": true, "hide_process_enabled": true, "hide_window_title": true, "anti_detection_enabled": true, "img_recognize_method": "stealth", "mask_memory_signature": true, "secure_process_name": true, "anti_memory_scan": true, "randomize_screenshot": true, "memory_cleaning_random": true, "process_hollowing_protection": true, "virtual_machine_detection": false, "network_traffic_obfuscation": true, "simulate_human_mouse": false, "dll_injection_protection": true, "hook_protection": true, "cooldown_time_random": false, "cooldown_time_min": 5, "cooldown_time_max": 15, "new_sub_rect_cooldown_random": {"0": false, "1": false, "2": false, "3": false}, "new_sub_rect_cooldown_min": {"0": 1, "1": 1, "2": 1, "3": 1}, "new_sub_rect_cooldown_max": {"0": 5, "1": 5, "2": 5, "3": 5}, "cooldown_random_range": 5, "new_sub_cooldown_random_range": 5, "sub_threshold_min": 0.6016155088852989, "sub_threshold_max": 0.7978998384491115, "threshold_mode": "range"}