import os
import sys
import subprocess
import shutil
import platform

# 检查PyInstaller是否已安装
try:
    import PyInstaller
except ImportError:
    print("正在安装PyInstaller...")
    subprocess.call([sys.executable, "-m", "pip", "install", "pyinstaller"])

# 检查其他必要的依赖库是否已安装
required_packages = [
    "opencv-python", "numpy", "pyautogui", "pillow", "psutil", "pywin32"
]

for package in required_packages:
    try:
        __import__(package.replace("-", "_"))
    except ImportError:
        print(f"正在安装 {package}...")
        subprocess.call([sys.executable, "-m", "pip", "install", package])

# 定义输出路径和图标路径
output_dir = os.path.expanduser("~/Desktop")
icon_path = r"C:\Users\<USER>\Desktop\55555/1.ico"

# 检查图标文件是否存在
if not os.path.exists(icon_path):
    print(f"错误: 图标文件 {icon_path} 不存在!")
    sys.exit(1)

# 检查config_manager.py是否存在
if not os.path.exists("config_manager.py"):
    print("错误: config_manager.py 不存在!")
    sys.exit(1)

# 根据操作系统确定路径分隔符
path_separator = ";" if platform.system() == "Windows" else ":"

# 构建PyInstaller命令
cmd = [
    "pyinstaller",
    "--noconfirm",
    "--onefile",
    "--windowed",
    f"--icon={icon_path}",
    f"--distpath={output_dir}",
    "--name=屏幕区域监控工具",
    "--add-data", f"config_manager.py{path_separator}.",  # 添加config_manager.py
    # 确保包含所有必要的库
    "--hidden-import", "cv2",
    "--hidden-import", "numpy",
    "--hidden-import", "pyautogui",
    "--hidden-import", "PIL",
    "--hidden-import", "psutil",
    "--hidden-import", "win32gui",
    "--hidden-import", "win32con",
    "--hidden-import", "win32api",
    "--hidden-import", "win32process",
    "--hidden-import", "win32security",
    "--hidden-import", "win32service",
    "--hidden-import", "win32ts",
    "--hidden-import", "win32event",
    "--hidden-import", "win32job",
    "--hidden-import", "pywintypes",
    "--hidden-import", "winreg",
    "lghub.py"
]

print("开始打包程序...")
print(f"使用图标: {icon_path}")
print(f"输出目录: {output_dir}")

try:
    # 执行PyInstaller命令
    subprocess.call(cmd)

    # 如果存在配置文件，复制到输出目录
    config_file = "config.json"
    if os.path.exists(config_file):
        print(f"复制配置文件 {config_file} 到输出目录...")
        shutil.copy(config_file, output_dir)

    # 清理临时文件
    print("清理临时文件...")
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("屏幕区域监控工具.spec"):
        os.remove("屏幕区域监控工具.spec")

    print(f"打包完成! 可执行文件已保存到: {os.path.join(output_dir, '屏幕区域监控工具.exe')}")
    print("如果程序无法运行，请检查是否安装了所有必要的依赖库。")
except Exception as e:
    print(f"打包过程中出错: {e}")
    sys.exit(1) 