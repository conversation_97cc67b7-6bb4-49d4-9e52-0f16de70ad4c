#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import ast
import sys

def check_syntax(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        ast.parse(content)
        print(f"✓ {filename} 语法检查通过")
        return True
    except SyntaxError as e:
        print(f"✗ {filename} 语法错误:")
        print(f"  行号: {e.lineno}")
        print(f"  位置: {e.offset}")
        print(f"  错误: {e.msg}")
        if e.text:
            print(f"  代码: {e.text.strip()}")
        return False
    except Exception as e:
        print(f"✗ {filename} 检查失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = "lghub.py"
    
    check_syntax(filename)
